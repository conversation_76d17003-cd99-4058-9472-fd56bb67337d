from typing import Annotated

import jwt
from configuration.settings import configuration
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2P<PERSON>wordBearer

from gc_dentist_shared.core.common.jwt_token import decode_jwt_token

router = APIRouter()


async def get_current_user_claims(
    token: str = Depends(OAuth2PasswordBearer(tokenUrl="token")),
):
    try:
        # 1. FIND PUBLIC KEY
        jwks_client_2 = jwt.PyJWKClient(configuration.AUTH_SERVICE_JWKS_URL)
        signing_key = jwks_client_2.get_signing_key_from_jwt(token)
        # 2. VERIFY TOKEN
        payload = decode_jwt_token(signing_key.key, configuration.JWT_ALGORITHM, token)
        return payload

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Invalid token: {e}",
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.get("/verify")
async def get_documents(claims: Annotated[dict, Depends(get_current_user_claims)]):
    user_id = claims.get("sub")
    user_roles = claims.get("roles", [])
    return {
        "user_id": user_id,
        "roles": user_roles,
    }
