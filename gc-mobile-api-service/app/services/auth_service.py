from typing import Optional

from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from configuration.logger.config import log
from configuration.settings import configuration
from core.common.utils import map_profile_with_scopes
from core.messages import INVALID_USERNAME_OR_PASSWORD, TENANT_NOT_FOUND
from db.db_connection import CentralDatabase, TenantDatabase
from schemas.auth_schema import (
    LoginRequest,
    OAuthTokenResponse,
    RefreshTokenRequest,
    TenantInfoSchema,
)
from sqlalchemy import select, text
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import (
    GlobalUser,
    OAuth2Client,
    OAuth2Token,
    TenantClinic,
    TenantPatientUserMapping,
)
from gc_dentist_shared.tenant_models import DoctorUser, PatientProfile


class AuthServices:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_tenant_with_tenant_slug_by_uuid(
        self, tenant_uuid: str
    ) -> TenantInfoSchema:
        db_session = await CentralDatabase().get_instance_db()
        async with db_session.begin():
            result = await db_session.execute(
                text(
                    "SELECT db_name FROM tenant_clinics WHERE tenant_uuid = :tenant_uuid"
                ),
                {"tenant_uuid": str(tenant_uuid)},
            )
            db_name = result.scalar_one_or_none()
            if not db_name:
                return None

            return TenantInfoSchema(db_name=db_name, tenant_uuid=tenant_uuid)

    async def get_tenant_with_tenant_slug(self, tenant_slug: str) -> TenantInfoSchema:
        db_session = await CentralDatabase().get_instance_db()
        async with db_session.begin():
            result = await db_session.execute(
                select(
                    TenantClinic.db_name,
                    TenantClinic.tenant_uuid,
                ).where(TenantClinic.tenant_slug == tenant_slug)
            )
            row = result.one_or_none()
            if not row:
                raise ValueError(TENANT_NOT_FOUND)

            db_name, tenant_uuid = row
            return TenantInfoSchema(db_name=db_name, tenant_uuid=str(tenant_uuid))

    async def login_for_doctor(
        self, tenant_slug: str, obj: LoginRequest
    ) -> OAuthTokenResponse:
        try:
            tenant = await self.get_tenant_with_tenant_slug(tenant_slug=tenant_slug)
            token = set_current_db_name(tenant.db_name)

            return await self.authenticate_doctor_user(tenant.tenant_uuid, obj)
        except ValueError as e:
            raise ValueError(str(e))
        except Exception as e:
            raise Exception(f"Authentication failed: {str(e)}")
        finally:
            reset_current_db_name(token)

    async def authenticate_doctor_user(
        self, tenant_uuid: str, obj: LoginRequest
    ) -> OAuthTokenResponse:
        session = await TenantDatabase.get_instance_tenant_db()
        async with session.begin():
            result = await session.execute(
                select(DoctorUser).where(
                    DoctorUser.username == obj.username,
                    DoctorUser.status.is_(True),
                )
            )
            user = result.scalar_one_or_none()
            if not user or not user.validate_password(obj.password):
                raise ValueError(INVALID_USERNAME_OR_PASSWORD)

            result_gobal_user = await session.execute(
                select(GlobalUser).where(
                    GlobalUser.username == obj.username,
                    GlobalUser.status.is_(True),
                )
            )
            global_user = result_gobal_user.scalar_one_or_none()
            if not global_user:
                raise ValueError(INVALID_USERNAME_OR_PASSWORD)

            default_client = await self.get_client_by_name(
                configuration.DEFAULT_CLIENT_NAME_FOR_WEB
            )
            token_data = {"sub": str(global_user.id), "tenant_uuid": str(tenant_uuid)}

            token_obj = self.generate_token(
                session, global_user.id, default_client.client_id, token_data
            )

            return OAuthTokenResponse(
                access_token=token_obj.access_token,
                refresh_token=token_obj.refresh_token,
                expires_in=configuration.ACCESS_TOKEN_EXPIRE_MINUTES,
            )

    async def get_patient_user_profile(
        self,
        gobal_user_id: int,
        tenant_db_name: str,
        tenant_uuid: str,
        scope: Optional[list] = None,
    ):
        try:
            scope = scope or []
            token = set_current_db_name(tenant_db_name)
            patient_user_id = await self.get_patient_user_mapping_by_tenant_uuid(
                gobal_user_id, tenant_uuid
            )
            if not patient_user_id:
                return None

            tenant_db_session = await TenantDatabase.get_instance_tenant_db()
            async with tenant_db_session():
                result = await tenant_db_session.execute(
                    select(PatientProfile).where(
                        PatientProfile.patient_user_id == patient_user_id
                    )
                )
                user_profile = result.scalar_one_or_none()
                data_resp = map_profile_with_scopes(user_profile, scope)
                return data_resp
        except ValueError as e:
            log.error("❌ get_patient_user_profile failed with value error: %s", str(e))
            return None
        except Exception as e:
            log.error("❌ get_patient_user_profile failed with error: %s", str(e))
            return None
        finally:
            reset_current_db_name(token)

    async def get_global_user_by_id(self, global_user_id):
        async with self.session():
            result = await self.session.execute(
                select(GlobalUser).where(
                    GlobalUser.id == global_user_id, GlobalUser.is_active.is_(True)
                )
            )
            global_user = result.scalar_one_or_none()
            return global_user

    async def get_patient_user_mapping_by_tenant_uuid(
        self, global_user_id: int, tenant_uuid: str
    ):
        async with self.session():
            result = await self.session.execute(
                select(TenantPatientUserMapping.patient_user_id).where(
                    TenantPatientUserMapping.global_user_id == global_user_id,
                    TenantPatientUserMapping.tenant_uuid == tenant_uuid,
                )
            )
            user_mapping_id = result.scalar_one_or_none()
            return user_mapping_id

    async def get_client_by_name(self, client_name: str) -> OAuth2Client:
        async with self.session():
            result = await self.session.execute(
                select(OAuth2Client).where(
                    OAuth2Client.name == client_name, OAuth2Client.is_active.is_(True)
                )
            )
            client = result.scalar_one_or_none()
            return client

    async def get_client(self, client_id):
        async with self.session():
            auth_client = await self.session.get(OAuth2Client, client_id)
            return auth_client

    async def get_refresh_token(self, refresh_token):
        async with self.session.begin():
            result = await self.session.execute(
                select(OAuth2Client).where(OAuth2Client.token == refresh_token)
            )
            return result.scalar_one_or_none()

    async def refresh_access_token(
        self,
        data: RefreshTokenRequest,
        old_refresh_token: OAuth2Token,
        auth_client: OAuth2Client,
    ):
        async with self.session.begin():
            token_data = {
                "sub": str(old_refresh_token.global_user_id),
                "tenant_uuid": str(data.tenant_uuid),
            }
            token_obj = self.generate_token(
                self.session,
                old_refresh_token.global_user_id,
                auth_client.client_id,
                token_data,
            )

            # Optional: revoke old refresh token
            old_refresh_token.revoked = True
            self.session.add(old_refresh_token)
            await self.session.commit()

            return OAuthTokenResponse(
                access_token=token_obj.access_token,
                refresh_token=token_obj.refresh_token,
                expires_in=configuration.ACCESS_TOKEN_EXPIRE_MINUTES,
            )

    # async def validate_user(
    #     self,
    #     bearer_token: str,
    # ) -> GlobalUser:

    #     try:
    #         payload = decode_jwt_token(bearer_token)
    #         if not payload:
    #             sub = payload.get("sub")
    #             tenant_uuid = payload.get("tenant_uuid")
    #             exp = payload.get("exp")
    #             if not sub or not tenant_uuid:
    #                 return None
    #             if datetime.now(timezone.utc).timestamp() > exp:
    #                 return None

    #             async with self.session():
    #                 result = await self.session.execute(
    #                     select(GlobalUser).where(GlobalUser.id == sub, GlobalUser.is_active == True)
    #                 )
    #             user = result.scalar_one_or_none()
    #             return user
    #     except Exception as e:
    #         log.error("❌ validate_user failed with error: %s", str(e))
    #         return None
