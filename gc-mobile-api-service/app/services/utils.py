# # app/utils.py
# import hashlib
# import random
# import uuid
# from datetime import datetime, timedelta

# from db.models.token import RefreshToken
# from jose import jwt
# from sqlalchemy.ext.asyncio import AsyncSession
# from sqlalchemy.future import select

# SECRET_KEY = "your-secret-key" # pragma: allowlist secret
# ALGORITHM = "HS256"
# OTP_STORE = {}  # In-memory. Use Redis for production


# def hash_password(password: str) -> str:
#     return hashlib.sha256(password.encode()).hexdigest()


# def verify_password(password: str, hashed: str) -> bool:
#     return hash_password(password) == hashed


# def generate_refresh_token() -> str:
#     return str(uuid.uuid4())


# async def store_refresh_token(db: AsyncSession, user_id: str, token: str):
#     expires_at = datetime.utcnow() + timedelta(days=7)
#     refresh_token = RefreshToken(user_id=user_id, token=token, expires_at=expires_at)
#     db.add(refresh_token)
#     await db.commit()


# async def is_valid_refresh_token(db: AsyncSession, token: str) -> RefreshToken | None:
#     result = await db.execute(
#         select(RefreshToken).where(
#             RefreshToken.token == token,
#             RefreshToken.revoked == False,
#             RefreshToken.expires_at > datetime.utcnow(),
#         )
#     )
#     return result.scalar_one_or_none()


# async def revoke_refresh_token(db: AsyncSession, token: str):
#     result = await db.execute(select(RefreshToken).where(RefreshToken.token == token))
#     token_obj = result.scalar_one_or_none()
#     if token_obj:
#         token_obj.revoked = True
#         await db.commit()


# def generate_otp(phone: str) -> str:
#     otp = f"{random.randint(100000, 999999)}"
#     OTP_STORE[phone] = otp
#     print(f"[DEBUG] OTP for {phone}: {otp}")
#     return otp


# def verify_otp(phone: str, otp: str) -> bool:
#     return OTP_STORE.get(phone) == otp
