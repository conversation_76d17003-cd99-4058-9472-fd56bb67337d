from db.db_connection import CentralDatabase
from sqlalchemy import text


async def get_db_name_for_tenant(tenant_uuid: str) -> str:
    db_session = await CentralDatabase().get_instance_db()
    async with db_session.begin():
        result = await db_session.execute(
            text("SELECT db_name FROM tenant_clinics WHERE tenant_uuid = :tenant_uuid"),
            {"tenant_uuid": str(tenant_uuid)},
        )

        db_name = result.scalar_one_or_none()
        return db_name
