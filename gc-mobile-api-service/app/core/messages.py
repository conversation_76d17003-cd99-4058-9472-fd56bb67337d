from enum import Enum

from gc_dentist_shared.core.common.i18n import i18n as _

CLIENT_NOT_FOUND = "Client is not found."
ERROR_PROCESS = "Request could not be processed, please try again."
TENANT_NOT_FOUND = "Tenant not found!"
INVALID_USERNAME_OR_PASSWORD = (
    "The provided username or password is incorrect."  # pragma: allowlist secret
)
X_TENANT_SLUG_IS_REQUIRED = "The X-Tenant-Slug header must be provided in the request."
INVALID_TOKEN = "Invalid token."
REVOKED_TOKEN_SUCCESS = "Revoked token successfully."
REVOKED_TOKEN_ERROR = "Failed to revoke token."
INVALID_REFRRESH_TOKEN = "Invalid refresh token or token is expired."
INVALID_CLIENT = "Invalid client."
INVALID_TENANT = "Invalid tenant."
INVALID_USER = "User inactive or not found."
INVALID_TOKEN_IS_REVOKED = "The token is revoked."
USER_NOT_FOUND = "User not found."
INVALID_REDIRECT_URL = "Invalid client or redirect URI."
ERROR_PROCESSING = "Request could not be processed, please try again."
INVALID_SCOPES = "Invalid scopes."
UNSUPPORT_RESPONSE_TYPE = "Unsupported response  type."
INVALID_AUTHORIZATION_CODE = "Invalid or expired authorization code"


class CustomMessageCode(Enum):
    """Custom message codes for application-specific errors and messages."""

    def __new__(cls, code, title, description):
        obj = object.__new__(cls)
        obj._value_ = code
        obj._title = title
        obj._description = description
        return obj

    @property
    def code(self):
        return self.value

    @property
    def title(self):
        # Using the i18n instance to translate the title
        return _(self._title)

    @property
    def description(self):
        return self._description

    def __str__(self):
        return f"{self.code} - {self.title}: {self.description}"
