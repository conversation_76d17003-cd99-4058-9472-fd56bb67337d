import hashlib
import hmac

from config.settings import configuration
from fastapi import Header, HTTPException, Request


def generate_hmac_signature(
    message: str, secret: str = configuration.COMMUNICATE_SECRET_KEY
) -> str:
    """Generate HMAC SHA256"""
    return hmac.new(secret.encode(), message.encode(), hashlib.sha256).hexdigest()


def verify_hmac_signature(signature: str, message: str) -> bool:
    """Verify HMAC"""
    expected_signature = generate_hmac_signature(
        message, configuration.COMMUNICATE_SECRET_KEY
    )
    return hmac.compare_digest(expected_signature, signature)


def hmac_authentication(
    x_timestamp: str = Header(None), x_signature: str = Header(None)
):
    """authentication HMAC"""
    if not x_timestamp or not x_signature:
        raise HTTPException(status_code=401, detail="Missing authentication headers")

    message = x_timestamp
    if not verify_hmac_signature(x_signature, message):
        raise HTTPException(status_code=401, detail="Invalid HMAC signature")


def hmac_authentication_dynamic_key(secret_key: str):
    async def verify_hmac_signature(request: Request):
        client_hmac = request.headers.get("X-HMAC-Signature")
        if not client_hmac:
            raise HTTPException(status_code=401, detail="HMAC signature missing")

        body = await request.body()
        computed_hmac = hmac.new(secret_key.encode(), body, hashlib.sha256).hexdigest()

        if not hmac.compare_digest(client_hmac, computed_hmac):
            raise HTTPException(status_code=401, detail="Invalid HMAC signature")

    return verify_hmac_signature
