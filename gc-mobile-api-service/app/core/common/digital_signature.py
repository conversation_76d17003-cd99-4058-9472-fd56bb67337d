from base64 import b64decode, b64encode

from config.logger.config import log
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding, rsa


class DigitalSignature:
    @classmethod
    def create_rsa_key_pair(cls):
        # create key pair
        private_key = rsa.generate_private_key(public_exponent=65537, key_size=2048)
        public_key = private_key.public_key()

        private_pem = private_key.private_bytes(
            serialization.Encoding.PEM,
            serialization.PrivateFormat.PKCS8,
            serialization.NoEncryption(),
        )

        public_pem = public_key.public_bytes(
            serialization.Encoding.PEM,
            serialization.PublicFormat.SubjectPublicKeyInfo,
        )

        return {
            "private_key": private_pem.decode(),
            "public_key": public_pem.decode(),
        }

    @classmethod
    def sign_with_secret(cls, private_key: str, message: str) -> str:

        private_key = serialization.load_pem_private_key(private_key, password=None)

        signature = private_key.sign(
            message.encode(), padding.PKCS1v15(), hashes.SHA256()
        )

        return b64encode(signature).decode()

    @classmethod
    def verify_signature_with_public_key(
        cls, public_key: str, message: str, signature: str
    ) -> bool:
        try:
            public_key = serialization.load_pem_public_key(public_key.encode())
            public_key.verify(
                b64decode(signature),
                message.encode(),
                padding.PKCS1v15(),
                hashes.SHA256(),
            )
            return True
        except Exception as e:
            log.error("❌ Error verify_signature_with_public_key: " + str(e))
            return False
