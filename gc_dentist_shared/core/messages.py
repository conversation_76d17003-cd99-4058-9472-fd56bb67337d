from enum import Enum

from gc_dentist_shared.core.common.i18n import i18n as _

INVALID_EMAIL_ADDRESS_NEWLINES = (
    "Invalid address; address parts cannot contain newlines."
)


class CustomMessageCode(Enum):
    """Custom message codes for application-specific errors and messages."""

    def __new__(cls, code, title, description):
        obj = object.__new__(cls)
        obj._value_ = code
        obj._title = title
        obj._description = description
        return obj

    @property
    def code(self):
        return self.value

    @property
    def title(self):
        # Using the i18n instance to translate the title
        return _(self._title)

    @property
    def description(self):
        return self._description

    def __str__(self):
        return f"{self.code} - {self.title}: {self.description}"

    # Invalid Input Errors
    INVALID_REQUEST_PAYLOAD = (
        4422,
        "Invalid request payload!",
        "The request payload is invalid or improperly formatted",
    )
    INVALID_DATE_STRING_TYPE = (
        4423,
        "Invalid date string type!",
        "The provided date string is not in the correct format or type.",
    )
    INVALID_DATETIME_TYPE = (
        4424,
        "Invalid datetime type!",
        "The provided datetime is not in the correct format or type.",
    )
    INVALID_UUID_STRING = (
        4425,
        "Invalid UUID string!",
        "The provided UUID string is not valid.",
    )

    # Authentication Errors
    FORBIDDEN_ERROR = (
        4403,
        "Permission Denied!",
        "You do not have the necessary permissions to perform this action or access this resource.",
    )

    # Server Errors
    UNKNOWN_ERROR = (
        5000,
        _("Unknown error!", "「不明なエラーです!"),
        "An unexpected error occurred.",
    )
    TWILIO_ERROR = (
        5001,
        "Twilio error!",
        "An error occurred while processing the Twilio request.",
    )
    TWILIO_SEND_MESSAGE_ERROR = (
        5002,
        "Twilio send message error!",
        "An error occurred while sending a message via Twilio.",
    )
    S3_BUCKET_ERROR = (
        5003,
        "S3 bucket error!",
        "An error occurred while interacting with the S3 bucket.",
    )
    S3_BUCKET_KEY_NOT_FOUND = (
        5004,
        "S3 bucket key not found!",
        "The specified key does not exist in the S3 bucket.",
    )
