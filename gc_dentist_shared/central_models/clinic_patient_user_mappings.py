from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Inte<PERSON>, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from gc_dentist_shared.base.central_declarative_base import CentralBase
from gc_dentist_shared.base.datetime_mixin import DateTimeMixin


class TenantPatientUserMapping(CentralBase, DateTimeMixin):
    __tablename__ = "tenant_user_mappings"

    id = Column(Integer, primary_key=True)
    global_user_id = Column(Integer, ForeignKey("global_users.id"), nullable=True)
    tenant_uuid = Column(UUID(as_uuid=True), ForeignKey("tenant_clinics.tenant_uuid"))
    patient_user_id = Column(Integer, nullable=False)
    global_user = relationship("GlobalUser", back_populates="user_mapping")
    is_active = Column(Boolean, default=True)
    provider_uuid = Column(
        UUID(as_uuid=True), ForeignKey("auth_providers.provider_uuid"), nullable=True
    )
    provider_subject = Column(String, nullable=True)
    provider = relationship("AuthProvider", back_populates="user_mappings")

    # username = Column(String, unique=False, nullable=False)
    # hashed_password = Column(String)
    # email = Column(String, unique=False)
