from sqlalchemy import Column, Integer, String

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class DocumentGroup(TenantBase, DateTimeMixin):
    __tablename__ = "document_group"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False, comment="Name of the document group")

    created_by = Column(
        Integer, nullable=True, comment="ID of the user who created the record"
    )
    updated_by = Column(
        Integer, nullable=True, comment="ID of the user who last updated the record"
    )
