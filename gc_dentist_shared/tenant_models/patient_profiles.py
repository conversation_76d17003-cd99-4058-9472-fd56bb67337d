from uuid import uuid4

from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, Integer, String

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class PatientProfile(TenantBase, DateTimeMixin):
    __tablename__ = "patient_profiles"

    id = Column(String, primary_key=True, default=lambda: str(uuid4()))
    patient_user_id = Column(Integer, ForeignKey("patient_users.id"), nullable=False)
    era_id = Column(Integer, ForeignKey("m_eras.id"), nullable=True)
    address_1 = Column(
        String(), default=None, comment="Data encrypted, ensure original length max 255"
    )
    address_2 = Column(
        String(), default=None, comment="Data encrypted, ensure original length max 255"
    )
    address_3 = Column(
        String(), default=None, comment="Data encrypted, ensure original length max 255"
    )
    prefecture_id = Column(Integer, Foreign<PERSON>ey("m_prefecture.id"), nullable=True)
    postal_code = Column(
        String(length=8),
        default=None,
        comment="Data encrypted, ensure original length max 8",
    )
    home_phone = Column(
        String, nullable=True, comment="Data encrypted, ensure original length max 255"
    )
    home_phone_hash = Column(String, nullable=True, comment="Home Phone hash, sha-256")
    parent_name = Column(String, nullable=True)
    phone = Column(
        String,
        nullable=True,
        unique=True,
        comment="Data encrypted, original value must be unique and max 255",
    )
    phone_hash = Column(String, nullable=True, comment="Phone hash, sha-256")
    email = Column(
        String, nullable=True, comment="Data encrypted, ensure original length max 255"
    )
    email_hash = Column(String, nullable=True, comment="Email hash, sha-256")
    gender = Column(Integer, nullable=True)
    date_of_birth = Column(
        String,
        nullable=True,
        comment="Data encrypted, ensure original length max 255",
    )
    date_of_birth_hash = Column(
        String, nullable=True, comment="Date of birth hash, sha-256"
    )
    last_name_kana = Column(String, nullable=True)
    first_name_kana = Column(String, nullable=True)
    last_name = Column(String, nullable=False)
    first_name = Column(String, nullable=False)
    created_by = Column(String, nullable=True)
    updated_by = Column(String, nullable=True)
    country_code = Column(String, nullable=True)
