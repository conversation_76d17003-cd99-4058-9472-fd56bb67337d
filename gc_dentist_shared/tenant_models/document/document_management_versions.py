from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Inte<PERSON>, String, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase
from gc_dentist_shared.base.user_audit_mixin import UserAuditMixin


class DocumentManagementVersion(TenantBase, DateTimeMixin, UserAuditMixin):
    __tablename__ = "document_management_versions"

    id = Column(Integer, primary_key=True, autoincrement=True)
    patient_user_id = Column(Integer, ForeignKey("patient_users.id"), nullable=False)
    document_management_id = Column(
        Integer, ForeignKey("document_managements.id"), nullable=False
    )
    document_data = Column(JSONB, nullable=False, comment="Data of the document")
    preview_document_data = Column(
        JSONB, nullable=True, comment="Preview(thumbnail) data of the document"
    )
    version = Column(Integer, nullable=False, comment="Version of the document")
    data_type = Column(
        Integer, nullable=False, comment="Type of the document. Enums: DocumentType"
    )
    version_uuid = Column(String, nullable=False, comment="UUID of the version")

    __table_args__ = (
        UniqueConstraint(
            "document_management_id",
            "version",
            name="uq_document_management_versions_document_management_id_version",
        ),
    )
