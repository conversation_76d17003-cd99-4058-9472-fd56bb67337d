from uuid import uuid4

from sqlalchemy import <PERSON>um<PERSON>, Date, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.dialects.postgresql import JSONB

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase
from gc_dentist_shared.base.user_audit_mixin import UserAuditMixin


class DocumentManagement(TenantBase, DateTimeMixin, UserAuditMixin):
    __tablename__ = "document_managements"

    id = Column(Integer, primary_key=True, autoincrement=True)
    patient_user_id = Column(Integer, ForeignKey("patient_users.id"), nullable=False)
    name = Column(String, nullable=False, comment="Name of the document")
    status = Column(
        Integer, nullable=False, comment="Status of the document. Enums: DocumentStatus"
    )
    document_group_id = Column(Integer, ForeignKey("document_group.id"), nullable=True)
    data_type = Column(
        Integer, nullable=False, comment="Type of the document. Enums: DocumentDataType"
    )
    document_data = Column(JSONB, nullable=False, comment="Data of the document")
    preview_document_data = Column(
        JSONB, nullable=True, comment="Preview(Thumbnail) data of the document"
    )
    examination_date = Column(
        Date, nullable=True, comment="Date of the examination/measurement"
    )
    medical_history_id = Column(
        Integer, ForeignKey("medical_histories.id"), nullable=True
    )
    display_mode = Column(
        Integer,
        nullable=False,
        comment="Display mode of the document. Enums: DocumentDisplayMode",
    )
    version_uuid = Column(
        String,
        nullable=False,
        default=lambda: str(uuid4()),
        comment="UUID of the latest version",
    )
    extra_data = Column(JSONB, nullable=True, comment="Extra data of the document")
