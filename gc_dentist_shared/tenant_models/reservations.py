from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Column,
    Date,
    Foreign<PERSON><PERSON>,
    Integer,
    String,
    Text,
    Time,
)

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class Reservation(TenantBase, DateTimeMixin):
    __tablename__ = "reservations"

    id = Column(Integer, primary_key=True, autoincrement=True)
    patient_user_id = Column(Integer, ForeignKey("patient_users.id"), nullable=False)

    external_reservation_code = Column(String, comment="External reservation code")
    external_patient_id = Column(String, comment="External patient identifier")
    group_uuid = Column(String, comment="UUID for reservation group")

    doctor_user_ids = Column(
        ARRAY(Integer), comment="List of doctor user IDs (PostgreSQL only)"
    )

    schedule_visit_date = Column(Date, comment="Scheduled visit date")
    schedule_visit_time = Column(Time, comment="Scheduled visit time")
    schedule_visit_minutes = Column(
        Integer, comment="Estimated duration of visit in minutes"
    )

    source = Column(
        String, comment="Source of the reservation (e.g., web, mobile, etc.)"
    )
    chair_number = Column(Integer, comment="Chair number for the reservation")
    status = Column(Integer, comment="Reservation status")

    note = Column(Text, comment="Additional notes for the reservation")
    extra_data = Column(JSON, comment="Extra JSON data for customization or metadata")

    created_by = Column(Integer)
    updated_by = Column(Integer)
