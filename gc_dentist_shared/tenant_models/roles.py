from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer  # type: ignore

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.tenant_models.master_base import MasterBaseModel


class Role(MasterBaseModel, DateTimeMixin):
    """Role table"""

    __tablename__ = "roles"

    role_scope = Column(
        Integer, default=None, comment="1: doctor, 2: patient", nullable=False
    )
    level = Column(Integer, default=1, comment="Level Of Role", nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
