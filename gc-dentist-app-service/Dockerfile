# Dockerfile for gc-dentist-service
FROM python:3.12-slim as builder

WORKDIR /build

COPY ./gc_dentist_shared ./gc_dentist_shared

COPY ./pyproject.toml .

RUN pip install --upgrade pip build

RUN python -m build

FROM python:3.12-slim as runner

RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    libffi-dev \
    libssl-dev \
    libpq-dev \
    python-dev-is-python3 \
    git \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

WORKDIR /app

# Copy dependency files
COPY ./gc-dentist-app-service/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY --from=builder /build/dist/ ./dist/
RUN pip install ./dist/*.whl && rm -rf ./dist

# Copy application files
COPY ./gc-dentist-app-service/app .

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
