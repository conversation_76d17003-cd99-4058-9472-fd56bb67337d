from datetime import date, datetime
from typing import ClassV<PERSON>, Optional, Union

from pydantic import BaseModel


class GetDocumentSchema(BaseModel):
    id: int
    patient_user_id: int
    name: str
    document_group_id: Optional[int] = None
    data_type: int
    document_data: Union[list[dict], list[str]]
    examination_date: date
    medical_history_id: Optional[int] = None
    version_uuid: str
    display_mode: int
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True
        json_encoders: ClassVar[dict] = {
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S") if v else None
        }


class DocumentListSchema(BaseModel):
    examination_date: date
    list_document: list[GetDocumentSchema]
