from typing import Optional

from pydantic import BaseModel, ConfigDict, field_validator


class DocumentManagementUpdateResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    patient_user_id: int
    name: Optional[str] = None
    status: Optional[int] = None
    data_type: Optional[int] = None
    document_data: Optional[list] = None
    document_group_id: Optional[int] = None
    medical_history_id: Optional[int] = None
    examination_date: Optional[str] = None
    display_mode: Optional[int] = None
    extra_data: Optional[dict] = None

    @field_validator("examination_date", mode="before")
    @classmethod
    def format_examination_date(cls, v):
        return v.isoformat() if v else None
