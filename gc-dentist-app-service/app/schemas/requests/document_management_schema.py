from datetime import date
from typing import Optional

from core.messages import CustomMessageCode
from pydantic import BaseModel, model_validator


class DocumentQueryParams(BaseModel):
    tag: Optional[str] = None
    group: Optional[str] = None


class DocumentPreviewQueryParams(BaseModel):
    document_ids: Optional[list[int]] = None
    examination_date: Optional[date] = None

    @model_validator(mode="after")
    def validate_at_least_one_param(self):
        if not self.document_ids and not self.examination_date:
            raise ValueError(
                CustomMessageCode.DOCUMENT_MANAGEMENT_GET_PREVIEW_INVALID_PARAMS.title
            )
        return self
