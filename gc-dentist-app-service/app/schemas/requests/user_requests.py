from typing import Any

from pydantic import BaseModel, conint, constr


class DoctorUserPayloads(BaseModel):
    username: constr(strip_whitespace=True, min_length=1)  # type: ignore
    password: constr(strip_whitespace=True, min_length=1)  # type: ignore
    role_id: conint(gt=0)  # type: ignore


class RolePayloads(BaseModel):
    name_json: dict[str, Any]
    sort: conint(gt=0)  # type: ignore
    role_scope: conint(gt=0)  # type: ignore
