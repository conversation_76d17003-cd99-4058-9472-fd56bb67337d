from typing import Optional

from core.messages import CustomMessageCode
from pydantic import BaseModel, Field, field_validator

from gc_dentist_shared.core.common.utils import ValidateDateString
from gc_dentist_shared.core.enums.document import (
    DocumentDataType,
    DocumentDisplayMode,
    DocumentStatus,
)


class DocumentDataSchema(BaseModel):
    file_index: str = Field(..., description="File index")
    file_key: str = Field(..., description="File key")
    file_size: int = Field(..., description="File size")


class DocumentManagementCreateSchema(BaseModel):
    patient_user_id: int = Field(..., description="Patient user ID")
    name: str = Field(
        ..., description="Name of the document", min_length=1, max_length=255
    )
    status: DocumentStatus = Field(
        default=DocumentStatus.DRAFT.value, description="Status of the document"
    )
    document_group_id: int = Field(..., description="Source of the document")
    data_type: DocumentDataType = Field(
        default=DocumentDataType.ORIGINAL.value, description="Type of the document"
    )
    document_data: list[DocumentDataSchema] = Field(
        ..., description="Data of the document"
    )
    display_mode: int = Field(
        default=DocumentDisplayMode.LIST.value,
        description="Display mode of the document",
    )
    examination_date: Optional[str] = Field(..., description="Examination date")
    extra_data: Optional[dict] = Field({}, description="Extra data of the document")

    @field_validator("examination_date", mode="before")
    @classmethod
    def validate_examination_date(cls, v: str) -> str:
        if v is not None:
            ValidateDateString.validate(v)
        return v

    @field_validator("status")
    @classmethod
    def validate_status(cls, v: DocumentStatus) -> DocumentStatus:
        if v not in [DocumentStatus.DRAFT, DocumentStatus.ACTIVATED]:
            raise ValueError(CustomMessageCode.DOCUMENT_MANAGEMENT_INVALID_STATUS.title)
        return v


class DocumentManagementUpdateSchema(BaseModel):
    version_uuid: Optional[str] = Field(None, description="Version UUID")
    patient_user_id: int = Field(..., description="Patient user ID")
    name: Optional[str] = Field(
        None, description="Name of the document", min_length=1, max_length=255
    )
    status: Optional[DocumentStatus] = Field(None, description="Status of the document")
    document_data: Optional[list[DocumentDataSchema]] = Field(
        None, description="Data of the document"
    )
    examination_date: Optional[str] = Field(None, description="Medical history ID")
    display_mode: Optional[int] = Field(
        None, description="Display mode of the document"
    )
    extra_data: Optional[dict] = Field(None, description="Extra data of the document")

    @field_validator("examination_date", mode="before")
    @classmethod
    def validate_examination_date(cls, v: str) -> str:
        if v is not None:
            ValidateDateString.validate(v)
        return v

    @field_validator("status")
    @classmethod
    def validate_status(cls, v: DocumentStatus) -> DocumentStatus:
        if v not in [DocumentStatus.DRAFT, DocumentStatus.ACTIVATED]:
            raise ValueError(CustomMessageCode.DOCUMENT_MANAGEMENT_INVALID_STATUS.title)
        return v
