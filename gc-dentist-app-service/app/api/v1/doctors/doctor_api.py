from typing import Annotated

from core.common.api_response import ApiResponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends, status  # type: ignore
from fastapi_pagination import Page
from schemas.requests.doctor_user_schema import (
    CreateDoctorPayloads,
    UpdateDoctorPayloads,
)
from schemas.responses.doctor_user_schema import DoctorListSchema, GetDoctorSchema
from services.doctor_service import DoctorService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post("", summary="Create doctor")
@version(1, 0)
async def create(
    data: CreateDoctorPayloads,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        service = DoctorService(db_session)

        await service.validate_create_doctor(data)

        doctor_user_id = await service.create(data)
        return ApiResponse.success(
            data={"doctor_user_id": doctor_user_id},
            message=CustomMessageCode.DOCTOR_CREATED_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error create doctor CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error in method create doctor: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.DOCTOR_CREATED_FAILED.title,
            message_code=CustomMessageCode.DOCTOR_CREATED_FAILED.code,
        )


@router.get("/{doctor_id:int}", response_model=GetDoctorSchema)
@version(1, 0)
async def get_profile(
    doctor_id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        service = DoctorService(db_session)
        result = await service.get_doctor_profile(doctor_id)
        return ApiResponse.success(data=result.model_dump(mode="json"))
    except CustomValueError as e:
        log.error(
            f"❌ Error get_doctor_profile CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error in method get_doctor_profile: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.DOCTOR_DETAIL_FAILED.title,
            message_code=CustomMessageCode.DOCTOR_DETAIL_FAILED.code,
        )


@router.get("", response_model=Page[DoctorListSchema])
@version(1, 0)
async def get_list_doctor(
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        doctor_service = DoctorService(db_session)
        paginated_result = await doctor_service.list_doctor()
        return ApiResponse.success(data=paginated_result.model_dump(mode="json"))
    except Exception as e:
        log.error(f"Error in method list_doctor: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.DOCTOR_GET_LIST_FAILED.title,
            message_code=CustomMessageCode.DOCTOR_GET_LIST_FAILED.code,
        )


@router.put("/{doctor_user_id:int}", summary="Edit Doctor User and Doctor Profile")
@version(1, 0)
async def update_doctor_profile(
    doctor_user_id: int,
    data: UpdateDoctorPayloads,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        service = DoctorService(db_session)
        doctor_user_id = await service.update_doctor_profile(doctor_user_id, data)
        return ApiResponse.success(
            data={"doctor_user_id": doctor_user_id},
            message=CustomMessageCode.DOCTOR_UPDATED_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error update_doctor_profile CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error in method update_doctor_profile: {str(e)}")
        return ApiResponse.error(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=CustomMessageCode.DOCTOR_UPDATED_FAILED.title,
            message_code=CustomMessageCode.DOCTOR_UPDATED_FAILED.code,
        )
