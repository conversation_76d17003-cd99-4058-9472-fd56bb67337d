from typing import Annotated

from configuration.settings import configuration
from core.common.api_response import Api<PERSON><PERSON>ponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends, Request  # type: ignore
from schemas.requests.clinic_schema import ClinicInfoSchema
from schemas.requests.user_requests import RolePayloads
from services.clinic_service import ClinicService
from services.user_service import UserService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common.hmac import depends_hmac_authentication
from gc_dentist_shared.core.constants import X_TENANT_UUID
from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post(
    "/role",
    dependencies=[Depends(depends_hmac_authentication(configuration=configuration))],
)
@version(1, 0)
async def create_role(
    data: RolePayloads,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        user_service = UserService(db_session)
        role = await user_service.create_role(data)

        return ApiResponse.success(
            data={"role_id": role.id},
            message=CustomMessageCode.ROLE_CREATED_SUCCESS.title,
        )

    except Exception as e:
        log.error("❌ Create Role Error: {}".format(str(e)))
        return ApiResponse.error(
            message=CustomMessageCode.ROLE_CREATED_FAILED.title,
            data={"detail": CustomMessageCode.ROLE_CREATED_FAILED.description},
        )


# @router.post("/initial")
# async def init_tenant(data: TenantPayloads):
#     try:

#         asyncio.create_task(process_migrate_tenant(data))
#         return JSONResponse(
#                 status_code=status.HTTP_200_OK,
#                 content={"detail": INIT_TENANT_PROCESS},
#             )
#     except Exception as e:
#         log.error("❌ Init Tenant Error: {}".format(str(e)))
#         return JSONResponse(
#                 status_code=status.HTTP_400_BAD_REQUEST,
#                 content={"detail": ERROR_PROCESS},
#             )

# @router.post("/apply/changes")
# async def init_tenant(data: TenantApplyChanges):
#     try:

#         asyncio.create_task(process_migrate_tenant(data.databases))
#         return JSONResponse(
#                 status_code=status.HTTP_200_OK,
#                 content={"detail": INIT_TENANT_PROCESS},
#             )
#     except Exception as e:
#         log.error("❌ Init Tenant Error: {}".format(str(e)))
#         return JSONResponse(
#                 status_code=status.HTTP_400_BAD_REQUEST,
#                 content={"detail": ERROR_PROCESS},
#             )


@router.post(
    "/admin",
    dependencies=[Depends(depends_hmac_authentication(configuration=configuration))],
)
@version(1, 0)
async def create_profile(
    data: ClinicInfoSchema,
    request: Request,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        user_service = ClinicService(db_session)
        tenant_uuid = request.headers.get(X_TENANT_UUID)
        id = await user_service.init_admin_clinic(data, tenant_uuid)

        return ApiResponse.success(
            data={"doctor_user_id": id},
            message=CustomMessageCode.CLINIC_CREATED_SUCCESS.title,
        )
    except Exception as e:
        log.error("❌ Create Profile Error: {}".format(str(e)))
        return ApiResponse.error(
            message=CustomMessageCode.CLINIC_CREATED_FAILED.title,
            data={"detail": CustomMessageCode.CLINIC_CREATED_FAILED.description},
        )
