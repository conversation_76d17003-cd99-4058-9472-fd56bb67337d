import time
from datetime import date
from unittest.mock import patch

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from fastapi import status
from tests.helpers.insert_data.insert_document_management import (
    unittest_insert_document,
)
from tests.helpers.insert_data.insert_patient import unittest_insert_patient


@pytest.fixture(scope="module")
def _headers(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }


@pytest_asyncio.fixture(scope="module")
async def setup_data(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)
        documents = [
            await unittest_insert_document(
                async_tenant_db_session_object, patient_user_id=patient_user_id
            )
            for _ in range(2)
        ]

        return {"patient_user_id": patient_user_id, "documents": documents}


@pytest.mark.asyncio
async def test_get_preview_documents_success_with_document_id(
    async_client, _headers, setup_data
):
    patient_user_id = setup_data.get("patient_user_id")
    documents = setup_data.get("documents")
    document_first = documents[0]
    document_id = document_first.get("document_id")

    response = await async_client.get(
        f"/v1_0/document-managements/patients/{patient_user_id}/preview",
        headers=_headers,
        params={"document_ids": [document_id]},
    )
    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True
    data = response_json["data"]
    first_item = data[0]

    fields_response = [
        "patient_user_id",
        "name",
        "document_group_id",
        "data_type",
        "document_data",
        "examination_date",
        "medical_history_id",
        "version_uuid",
        "display_mode",
    ]
    for field in fields_response:
        assert field in first_item


@pytest.mark.asyncio
async def test_get_preview_documents_success_with_examination_date(
    async_client, _headers, setup_data
):
    patient_user_id = setup_data.get("patient_user_id")

    response = await async_client.get(
        f"/v1_0/document-managements/patients/{patient_user_id}/preview",
        headers=_headers,
        params={"examination_date": date.today()},
    )
    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True
    data = response_json["data"]
    second_item = data[1]

    fields_response = [
        "patient_user_id",
        "name",
        "document_group_id",
        "data_type",
        "document_data",
        "examination_date",
        "medical_history_id",
        "version_uuid",
        "display_mode",
    ]
    for field in fields_response:
        assert field in second_item


@pytest.mark.asyncio
async def test_get_preview_documents_success_empty_data(
    async_client, _headers, setup_data
):
    patient_user_id = setup_data.get("patient_user_id")
    document_id = int(time.time())

    response = await async_client.get(
        f"/v1_0/document-managements/patients/{patient_user_id}/preview",
        headers=_headers,
        params={"document_ids": document_id},
    )
    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True
    data = response_json["data"]
    assert len(data) == 0


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "query_params, status_code",
    [
        (None, 422),
        ({"document_ids": None}, 422),
        ({"document_ids": []}, 422),
        ({"document_ids": "test@examplecom"}, 422),
        ({"examination_date": "2025/12/12"}, 422),
        ({"examination_date": "2025-13-12"}, 422),
        ({"examination_date": None}, 422),
    ],
)
async def test_get_preview_documents_failed_validation_params(
    async_client, _headers, setup_data, query_params, status_code
):
    patient_user_id = setup_data.get("patient_user_id")

    response = await async_client.get(
        f"/v1_0/document-managements/patients/{patient_user_id}/preview",
        headers=_headers,
        params=query_params,
    )
    assert response.status_code == status_code


@pytest.mark.asyncio
async def test_get_preview_documents_exception_error(
    async_client, _headers, setup_data
):
    """Test general exception handling"""
    # Patch the service to throw a generic Exception
    with patch(
        "services.document_managements.document_management_service.DocumentManagementService.get_preview_documents",
        side_effect=Exception("Something went wrong"),
    ):
        patient_user_id = setup_data.get("patient_user_id")
        document = setup_data.get("documents")
        first_item = document[0]
        document_id = first_item.get("document_id")

        response = await async_client.get(
            f"/v1_0/document-managements/patients/{patient_user_id}/preview",
            headers=_headers,
            params={"document_ids": [document_id]},
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            response.json()["messageCode"]
            == CustomMessageCode.DOCUMENT_MANAGEMENT_GET_PREVIEW_FAILED.code
        )


@pytest.mark.asyncio
async def test_cleaned_up(async_tenant_db_session_object, truncate_table):
    await truncate_table(
        async_tenant_db_session_object,
        [
            "patient_profiles",
            "patient_users",
            "document_group",
            "document_managements",
        ],
    )
