import time
import uuid
from datetime import date
from random import randint
from unittest.mock import patch

import pytest
from core.messages import CustomMessageCode
from fastapi import status
from sqlalchemy import select
from tests.helpers.insert_data.insert_patient import (
    unittest_insert_patient,
    unittest_remove_patient_by_ids,
)

from gc_dentist_shared.core.enums.document import (
    DocumentDataType,
    DocumentDisplayMode,
    DocumentStatus,
)
from gc_dentist_shared.tenant_models import DocumentGroup, DocumentManagement


@pytest.fixture(scope="module")
def _headers(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }


@pytest.fixture(scope="function")
def valid_payload():
    """Fixture to build document management payload with default values"""
    return {
        "patient_user_id": 1,  # Will be overridden in tests
        "name": f"Test Document {int(time.time())}",
        "status": DocumentStatus.DRAFT.value,
        "document_group_id": 1,  # Will be overridden in tests
        "data_type": DocumentDataType.ORIGINAL.value,
        "document_data": [
            {
                "file_index": str(uuid.uuid4()),
                "file_key": f"test_file_{int(time.time())}.pdf",
                "file_size": randint(1000, 50000),
            }
        ],
        "display_mode": DocumentDisplayMode.LIST.value,
        "examination_date": date.today().isoformat(),
        "extra_data": {"test_key": "test_value"},
    }


@pytest.fixture(scope="function")
async def setup_test_data(async_tenant_db_session_object):
    """Setup test data including patient and document group"""
    async with async_tenant_db_session_object.begin():
        # Create test patient
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)

        # Create test document group
        group_data = {"name": f"Test Group {int(time.time())}"}
        document_group = DocumentGroup(**group_data)
        async_tenant_db_session_object.add(document_group)
        await async_tenant_db_session_object.flush()
        await async_tenant_db_session_object.refresh(document_group)

    return {
        "patient_user_id": patient_user_id,
        "document_group_id": document_group.id,
    }


@pytest.mark.asyncio
async def test_create_document_management_success(
    async_client, _headers, valid_payload, setup_test_data, async_tenant_db_session_object
):
    """Test successful document management creation with database verification"""
    # Update payload with test data
    valid_payload["patient_user_id"] = setup_test_data["patient_user_id"]
    valid_payload["document_group_id"] = setup_test_data["document_group_id"]

    response = await async_client.post(
        "/v1_0/document-managements", json=valid_payload, headers=_headers
    )

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert response_data["success"] is True
    assert "document_management_id" in response_data["data"]
    assert response_data["message"] == CustomMessageCode.DOCUMENT_MANAGEMENT_CREATED_SUCCESS.title

    document_id = response_data["data"]["document_management_id"]

    # Verify database state
    async with async_tenant_db_session_object.begin():
        document = await async_tenant_db_session_object.scalar(
            select(DocumentManagement).where(DocumentManagement.id == document_id)
        )
        assert document is not None
        assert document.patient_user_id == valid_payload["patient_user_id"]
        assert document.name == valid_payload["name"]
        assert document.status == valid_payload["status"]
        assert document.document_group_id == valid_payload["document_group_id"]
        assert document.data_type == valid_payload["data_type"]
        assert document.display_mode == valid_payload["display_mode"]
        assert document.examination_date.isoformat() == valid_payload["examination_date"]
        assert document.extra_data == valid_payload["extra_data"]
        assert document.version_uuid is not None

        # Verify document_data structure
        expected_doc_data = [{data["file_index"]: data["file_key"]} for data in valid_payload["document_data"]]
        assert document.document_data == expected_doc_data

    # Cleanup
    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[setup_test_data["patient_user_id"]]
        )


@pytest.mark.asyncio
async def test_create_document_management_with_activated_status(
    async_client, _headers, valid_payload, setup_test_data, async_tenant_db_session_object
):
    """Test document creation with ACTIVATED status"""
    valid_payload["patient_user_id"] = setup_test_data["patient_user_id"]
    valid_payload["document_group_id"] = setup_test_data["document_group_id"]
    valid_payload["status"] = DocumentStatus.ACTIVATED.value

    response = await async_client.post(
        "/v1_0/document-managements", json=valid_payload, headers=_headers
    )

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert response_data["success"] is True

    # Cleanup
    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[setup_test_data["patient_user_id"]]
        )


@pytest.mark.asyncio
async def test_create_document_management_with_multiple_files(
    async_client, _headers, valid_payload, setup_test_data, async_tenant_db_session_object
):
    """Test document creation with multiple files in document_data"""
    valid_payload["patient_user_id"] = setup_test_data["patient_user_id"]
    valid_payload["document_group_id"] = setup_test_data["document_group_id"]
    valid_payload["document_data"] = [
        {
            "file_index": str(uuid.uuid4()),
            "file_key": f"file1_{int(time.time())}.pdf",
            "file_size": 1000,
        },
        {
            "file_index": str(uuid.uuid4()),
            "file_key": f"file2_{int(time.time())}.jpg",
            "file_size": 2000,
        },
        {
            "file_index": str(uuid.uuid4()),
            "file_key": f"file3_{int(time.time())}.png",
            "file_size": 3000,
        },
    ]

    response = await async_client.post(
        "/v1_0/document-managements", json=valid_payload, headers=_headers
    )

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert response_data["success"] is True

    # Verify multiple files are stored correctly
    document_id = response_data["data"]["document_management_id"]
    async with async_tenant_db_session_object.begin():
        document = await async_tenant_db_session_object.scalar(
            select(DocumentManagement).where(DocumentManagement.id == document_id)
        )
        assert len(document.document_data) == 3

    # Cleanup
    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[setup_test_data["patient_user_id"]]
        )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "missing_field",
    [
        "patient_user_id",
        "name",
        "document_group_id",
        "document_data",
        "examination_date",
    ],
)
async def test_create_document_management_missing_required_fields(
    async_client, _headers, valid_payload, missing_field
):
    """Test validation for missing required fields"""
    # Remove the required field
    payload = valid_payload.copy()
    del payload[missing_field]

    response = await async_client.post(
        "/v1_0/document-managements", json=payload, headers=_headers
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "field_name, invalid_value, expected_status",
    [
        ("patient_user_id", "invalid_id", 422),
        ("patient_user_id", -1, 422),
        ("patient_user_id", 0, 422),
        ("name", "", 422),  # min_length=1
        ("name", "x" * 256, 422),  # max_length=255
        ("document_group_id", "invalid_id", 422),
        ("document_group_id", -1, 422),
        ("data_type", 999, 422),  # Invalid enum value
        ("status", 999, 422),  # Invalid enum value
        ("display_mode", "invalid_mode", 422),
        ("examination_date", "invalid-date", 422),
        ("examination_date", "2025-13-01", 422),  # Invalid month
        ("examination_date", "2025-02-30", 422),  # Invalid day
        ("document_data", [], 422),  # Empty list not allowed
        ("document_data", "not_a_list", 422),
    ],
)
async def test_create_document_management_invalid_field_values(
    async_client, _headers, valid_payload, field_name, invalid_value, expected_status
):
    """Test validation for invalid field values"""
    payload = valid_payload.copy()
    payload[field_name] = invalid_value

    response = await async_client.post(
        "/v1_0/document-managements", json=payload, headers=_headers
    )

    assert response.status_code == expected_status


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "invalid_document_data",
    [
        [{"file_index": "test", "file_size": 100}],  # Missing file_key
        [{"file_key": "test.pdf", "file_size": 100}],  # Missing file_index
        [{"file_index": "test", "file_key": "test.pdf"}],  # Missing file_size
        [{"file_index": "", "file_key": "test.pdf", "file_size": 100}],  # Empty file_index
        [{"file_index": "test", "file_key": "", "file_size": 100}],  # Empty file_key
        [{"file_index": "test", "file_key": "test.pdf", "file_size": -1}],  # Negative file_size
        [{"file_index": "test", "file_key": "test.pdf", "file_size": "invalid"}],  # Invalid file_size type
    ],
)
async def test_create_document_management_invalid_document_data_structure(
    async_client, _headers, valid_payload, invalid_document_data
):
    """Test validation for invalid document_data structure"""
    payload = valid_payload.copy()
    payload["document_data"] = invalid_document_data

    response = await async_client.post(
        "/v1_0/document-managements", json=payload, headers=_headers
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.asyncio
async def test_create_document_management_nonexistent_patient(
    async_client, _headers, valid_payload, setup_test_data
):
    """Test creation with non-existent patient_user_id"""
    payload = valid_payload.copy()
    payload["patient_user_id"] = 999999999  # Non-existent patient
    payload["document_group_id"] = setup_test_data["document_group_id"]

    response = await async_client.post(
        "/v1_0/document-managements", json=payload, headers=_headers
    )

    # Should return error from service validation
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_data = response.json()
    assert response_data["success"] is False
    assert response_data["message"] == CustomMessageCode.DOCUMENT_MANAGEMENT_CREATED_FAILED.title


@pytest.mark.asyncio
async def test_create_document_management_nonexistent_document_group(
    async_client, _headers, valid_payload, setup_test_data, async_tenant_db_session_object
):
    """Test creation with non-existent document_group_id"""
    payload = valid_payload.copy()
    payload["patient_user_id"] = setup_test_data["patient_user_id"]
    payload["document_group_id"] = 999999999  # Non-existent document group

    response = await async_client.post(
        "/v1_0/document-managements", json=payload, headers=_headers
    )

    # Should return error from service validation
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_data = response.json()
    assert response_data["success"] is False

    # Cleanup
    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[setup_test_data["patient_user_id"]]
        )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "file_size",
    [
        0,  # Zero file size
        1,  # Minimum file size
        1024 * 1024,  # 1MB
        10 * 1024 * 1024,  # 10MB
        100 * 1024 * 1024,  # 100MB - large file
    ],
)
async def test_create_document_management_various_file_sizes(
    async_client, _headers, valid_payload, setup_test_data, file_size, async_tenant_db_session_object
):
    """Test document creation with various file sizes"""
    payload = valid_payload.copy()
    payload["patient_user_id"] = setup_test_data["patient_user_id"]
    payload["document_group_id"] = setup_test_data["document_group_id"]
    payload["document_data"][0]["file_size"] = file_size

    response = await async_client.post(
        "/v1_0/document-managements", json=payload, headers=_headers
    )

    if file_size == 0:
        # Zero file size might be invalid depending on business rules
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_422_UNPROCESSABLE_ENTITY]
    else:
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["success"] is True

    # Cleanup
    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[setup_test_data["patient_user_id"]]
        )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "date_format",
    [
        "2025-08-17",  # ISO format
        "2025-12-31",  # End of year
        "2025-01-01",  # Start of year
        "2025-02-28",  # Non-leap year February
        "2024-02-29",  # Leap year February
    ],
)
async def test_create_document_management_valid_date_formats(
    async_client, _headers, valid_payload, setup_test_data, date_format, async_tenant_db_session_object
):
    """Test document creation with various valid date formats"""
    payload = valid_payload.copy()
    payload["patient_user_id"] = setup_test_data["patient_user_id"]
    payload["document_group_id"] = setup_test_data["document_group_id"]
    payload["examination_date"] = date_format

    response = await async_client.post(
        "/v1_0/document-managements", json=payload, headers=_headers
    )

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert response_data["success"] is True

    # Cleanup
    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[setup_test_data["patient_user_id"]]
        )


@pytest.mark.asyncio
async def test_create_document_management_with_empty_extra_data(
    async_client, _headers, valid_payload, setup_test_data, async_tenant_db_session_object
):
    """Test document creation with empty extra_data"""
    payload = valid_payload.copy()
    payload["patient_user_id"] = setup_test_data["patient_user_id"]
    payload["document_group_id"] = setup_test_data["document_group_id"]
    payload["extra_data"] = {}

    response = await async_client.post(
        "/v1_0/document-managements", json=payload, headers=_headers
    )

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert response_data["success"] is True

    # Cleanup
    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[setup_test_data["patient_user_id"]]
        )


@pytest.mark.asyncio
async def test_create_document_management_with_complex_extra_data(
    async_client, _headers, valid_payload, setup_test_data, async_tenant_db_session_object
):
    """Test document creation with complex extra_data structure"""
    payload = valid_payload.copy()
    payload["patient_user_id"] = setup_test_data["patient_user_id"]
    payload["document_group_id"] = setup_test_data["document_group_id"]
    payload["extra_data"] = {
        "metadata": {
            "device_info": {
                "model": "Scanner Pro 2000",
                "version": "1.2.3",
                "settings": {
                    "resolution": 300,
                    "color_mode": "RGB",
                    "compression": "lossless"
                }
            },
            "scan_date": "2025-08-17T10:30:00Z",
            "operator": "Dr. Smith"
        },
        "tags": ["urgent", "follow-up", "x-ray"],
        "notes": "Patient showed improvement since last visit"
    }

    response = await async_client.post(
        "/v1_0/document-managements", json=payload, headers=_headers
    )

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert response_data["success"] is True

    # Verify complex extra_data is stored correctly
    document_id = response_data["data"]["document_management_id"]
    async with async_tenant_db_session_object.begin():
        document = await async_tenant_db_session_object.scalar(
            select(DocumentManagement).where(DocumentManagement.id == document_id)
        )
        assert document.extra_data == payload["extra_data"]

    # Cleanup
    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[setup_test_data["patient_user_id"]]
        )


@pytest.mark.asyncio
async def test_create_document_management_service_exception(
    async_client, _headers, valid_payload
):
    """Test general exception handling when service throws an error"""
    with patch(
        "services.document_managements.document_management_service.DocumentManagementService.create_document_management",
        side_effect=Exception("Database connection failed"),
    ):
        response = await async_client.post(
            "/v1_0/document-managements", json=valid_payload, headers=_headers
        )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_data = response.json()
    assert response_data["success"] is False
    assert response_data["message"] == CustomMessageCode.DOCUMENT_MANAGEMENT_CREATED_FAILED.title
    assert "detail" in response_data["data"]


@pytest.mark.asyncio
async def test_create_document_management_validation_service_exception(
    async_client, _headers, valid_payload
):
    """Test exception handling during validation phase"""
    with patch(
        "services.document_managements.document_management_service.DocumentManagementService._validate_before_create_document",
        side_effect=Exception("Validation service unavailable"),
    ):
        response = await async_client.post(
            "/v1_0/document-managements", json=valid_payload, headers=_headers
        )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    response_data = response.json()
    assert response_data["success"] is False
    assert response_data["message"] == CustomMessageCode.DOCUMENT_MANAGEMENT_CREATED_FAILED.title


@pytest.mark.asyncio
async def test_create_document_management_maximum_files_edge_case(
    async_client, _headers, valid_payload, setup_test_data, async_tenant_db_session_object
):
    """Test document creation with maximum number of files (edge case)"""
    payload = valid_payload.copy()
    payload["patient_user_id"] = setup_test_data["patient_user_id"]
    payload["document_group_id"] = setup_test_data["document_group_id"]

    # Create a large number of files to test system limits
    max_files = 50  # Reasonable limit for testing
    payload["document_data"] = [
        {
            "file_index": f"file_{i}_{uuid.uuid4()}",
            "file_key": f"test_file_{i}_{int(time.time())}.pdf",
            "file_size": randint(1000, 5000),
        }
        for i in range(max_files)
    ]

    response = await async_client.post(
        "/v1_0/document-managements", json=payload, headers=_headers
    )

    # Should handle large number of files gracefully
    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert response_data["success"] is True

    # Verify all files are stored
    document_id = response_data["data"]["document_management_id"]
    async with async_tenant_db_session_object.begin():
        document = await async_tenant_db_session_object.scalar(
            select(DocumentManagement).where(DocumentManagement.id == document_id)
        )
        assert len(document.document_data) == max_files

    # Cleanup
    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[setup_test_data["patient_user_id"]]
        )


@pytest.mark.asyncio
async def test_create_document_management_special_characters_in_name(
    async_client, _headers, valid_payload, setup_test_data, async_tenant_db_session_object
):
    """Test document creation with special characters in name"""
    payload = valid_payload.copy()
    payload["patient_user_id"] = setup_test_data["patient_user_id"]
    payload["document_group_id"] = setup_test_data["document_group_id"]
    payload["name"] = "Test Document with Special Chars: !@#$%^&*()_+-=[]{}|;':\",./<>?`~"

    response = await async_client.post(
        "/v1_0/document-managements", json=payload, headers=_headers
    )

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert response_data["success"] is True

    # Verify special characters are preserved
    document_id = response_data["data"]["document_management_id"]
    async with async_tenant_db_session_object.begin():
        document = await async_tenant_db_session_object.scalar(
            select(DocumentManagement).where(DocumentManagement.id == document_id)
        )
        assert document.name == payload["name"]

    # Cleanup
    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[setup_test_data["patient_user_id"]]
        )


@pytest.mark.asyncio
async def test_cleanup_all_test_data(async_tenant_db_session_object, truncate_table):
    """Cleanup all test data after test suite completion"""
    await truncate_table(
        async_tenant_db_session_object,
        [
            "document_managements",
            "document_management_versions",
            "document_group",
            "patient_profiles",
            "patient_users",
        ],
    )
