import uuid
from random import randint

import pytest
import pytest_asyncio

from gc_dentist_shared.core.enums.document import DocumentStatus, DocumentDisplayMode
from tests.helpers.insert_data.insert_patient import unittest_insert_patient


@pytest_asyncio.fixture(scope="module")
async def setup_data(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)
    return {"patient_user_id": patient_user_id}


@pytest.mark.asyncio
async def test_create_document_management_success(async_client, _headers_tenant_uuid):
    payload = {
        "patient_user_id": 1,
        "name": str(uuid.uuid4()),
        "status": DocumentStatus.DRAFT.value,
        "data_type": 1,
        "document_data": [
            {
                "file_index": str(uuid.uuid4()),
                "file_key": str(uuid.uuid4()),
                "file_size": randint(1, 1000),
            }
        ],
        "display_mode": DocumentDisplayMode.LIST.value,
        "version_uuid": str(uuid.uuid4()),
        "examination_date": "2025-08-12",
    }

    response = await async_client.post(
        "/v1_0/document-managements", json=payload, headers=_headers_tenant_uuid
    )
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["success"] is True
    assert response_json["data"]["document_management_id"] is not None
