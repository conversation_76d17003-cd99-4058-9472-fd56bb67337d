from unittest.mock import patch

import pytest
from fastapi import status
from tests.helpers.insert_data.insert_patient import (
    unittest_insert_patient,
    unittest_remove_patient_by_ids,
)


@pytest.fixture
def non_existent_patient_id():
    """Return an ID that does not exist in the DB"""
    return 999999999


@pytest.fixture(scope="module")
def _headers(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }


@pytest.mark.asyncio
async def test_get_detail_patient_profile_success_structure(
    async_client, async_tenant_db_session_object, _headers
):
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)

    response = await async_client.get(
        f"/v1_0/patients/{patient_user_id}", headers=_headers
    )
    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True
    assert response_json["data"] is not None

    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[patient_user_id]
        )


@pytest.mark.asyncio
async def test_get_detail_patient_profile_data_integrity(
    async_client, async_tenant_db_session_object, _headers
):
    patient_data = {
        "last_name": "Nguyen",
        "first_name": "Van",
        "last_name_kana": "グエン",
        "first_name_kana": "バン",
        "home_phone": "**********",
        "phone": "**********",
        "email": "<EMAIL>",
        "gender": 1,
        "date_of_birth": "1990/01/01",
        "prefecture_id": 1,
        "postal_code": "1234567",
        "address_1": "Hanoi",
        "address_2": "Ba Dinh",
        "address_3": "So 1",
        "parent_name": "Nguyen Van A",
        "country_code": "+84",
    }
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(
            async_tenant_db_session_object,
            custom_profile_fields=patient_data,
        )

    response = await async_client.get(
        f"/v1_0/patients/{patient_user_id}", headers=_headers
    )
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["success"] is True
    data = response_json["data"]

    assert "username" in data
    assert "clinical_no" in data
    assert "is_adult" in data
    assert "profile" in data
    profile = data["profile"]
    for key, value in patient_data.items():
        assert profile.get(key) == value

    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[patient_user_id]
        )


@pytest.mark.asyncio
async def test_get_detail_patient_profile_not_found(
    async_client, _headers, non_existent_patient_id
):

    response = await async_client.get(
        f"/v1_0/patients/{non_existent_patient_id}", headers=_headers
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_json = response.json()
    assert response_json["messageCode"] == 60003


@pytest.mark.asyncio
async def test_get_detail_patient_profile_deleted_patient(
    async_client, async_tenant_db_session_object, _headers
):
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(
            async_tenant_db_session_object, custom_user_fields={"status": False}
        )

    response = await async_client.get(
        f"/v1_0/patients/{patient_user_id}", headers=_headers
    )
    assert response.status_code == 404
    response_json = response.json()
    assert response_json["messageCode"] == 60003

    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[patient_user_id]
        )


@pytest.mark.asyncio
async def test_get_detail_patient_profile_exception_error(async_client, _headers):
    """Test general exception handling"""
    # Patch the service to throw a generic Exception
    with patch(
        "services.patient_service.PatientService.get_patient_profile",
        side_effect=Exception("Something went wrong"),
    ):
        response = await async_client.get("/v1_0/patients/1", headers=_headers)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["messageCode"] == 60009
