import uuid
from datetime import datetime, timezone
from unittest.mock import patch

import pytest
from configuration.settings import configuration
from core.constants import PATIENT_FIELDS_ENCRYPTED
from fastapi import status
from tests.helpers.insert_data.insert_patient import (
    unittest_insert_patient,
    unittest_insert_patient_response_info,
    unittest_remove_patient_by_ids,
)

from gc_dentist_shared.core.common.hmac import generate_hmac_signature


def get_headers(tenant_uuid, valid_headers=None):
    """Create headers for API requests"""
    headers = {"X-Tenant-UUID": tenant_uuid}
    if valid_headers:
        headers.update(valid_headers)
    return headers


@pytest.fixture
def valid_headers():
    """Create valid headers with HMAC signature"""
    time_request = str(int(datetime.now(timezone.utc).timestamp() * 1000))
    signature = generate_hmac_signature(
        message=str(time_request), secret=configuration.COMMUNICATE_SECRET_KEY
    )
    return {
        "X-Signature": signature,
        "X-Timestamp": str(time_request),
    }


@pytest.fixture(scope="module")
def _headers(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }


@pytest.mark.asyncio
async def test_get_list_success_structure(
    async_client, async_tenant_db_session_object, _headers, truncate_table
):
    await truncate_table(
        async_tenant_db_session_object, ["patient_profiles", "patient_users"]
    )

    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)

    response = await async_client.get("/v1_0/patients", headers=_headers)
    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True
    assert response_json["data"] is not None

    data = response_json["data"]
    assert isinstance(data["items"], list)
    assert "total" in data
    assert "page" in data
    assert "size" in data

    assert data["total"] == 1
    assert len(data["items"]) == 1

    patient_item = data["items"][0]
    assert patient_item["id"] == patient_user_id
    assert "patient_no" in patient_item
    assert "profile" in patient_item
    assert isinstance(patient_item["profile"], dict)

    profile = patient_item["profile"]
    assert "last_name" in profile
    assert "first_name" in profile
    assert "last_name_kana" in profile
    assert "first_name_kana" in profile
    assert "gender" in profile
    assert "date_of_birth" in profile

    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, patient_ids=[patient_user_id]
        )


@pytest.mark.asyncio
async def test_get_list_empty_when_no_active_patients(
    async_client, async_tenant_db_session_object, _headers
):
    async with async_tenant_db_session_object.begin():
        await unittest_insert_patient(
            async_tenant_db_session_object, custom_user_fields={"status": False}
        )

    response = await async_client.get("/v1_0/patients", headers=_headers)

    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True

    data = response_json["data"]
    assert data["total"] == 0
    assert len(data["items"]) == 0


@pytest.mark.asyncio
async def test_get_list_pagination_logic(
    async_client, async_tenant_db_session_object, _headers
):
    async with async_tenant_db_session_object.begin():
        patient_id_1 = await unittest_insert_patient(async_tenant_db_session_object)
        patient_id_2 = await unittest_insert_patient(async_tenant_db_session_object)
        patient_id_3 = await unittest_insert_patient(async_tenant_db_session_object)

    response_page1 = await async_client.get(
        "/v1_0/patients?page=1&size=2", headers=_headers
    )

    assert response_page1.status_code == status.HTTP_200_OK
    data_page1 = response_page1.json()["data"]

    assert data_page1["total"] == 3
    assert len(data_page1["items"]) == 2
    assert data_page1["page"] == 1
    assert data_page1["size"] == 2

    assert data_page1["items"][0]["id"] == patient_id_3
    assert data_page1["items"][1]["id"] == patient_id_2

    response_page2 = await async_client.get(
        "/v1_0/patients?page=2&size=2", headers=_headers
    )

    assert response_page2.status_code == status.HTTP_200_OK
    data_page2 = response_page2.json()["data"]

    assert data_page2["total"] == 3
    assert len(data_page2["items"]) == 1
    assert data_page2["page"] == 2
    assert data_page2["items"][0]["id"] == patient_id_1

    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object,
            patient_ids=[patient_id_1, patient_id_2, patient_id_3],
        )


@pytest.mark.asyncio
async def test_get_list_with_valid_decrypt_info(
    async_client, async_tenant_db_session_object, _headers
):
    async with async_tenant_db_session_object.begin():
        result = await unittest_insert_patient_response_info(
            async_tenant_db_session_object
        )

    response = await async_client.get("/v1_0/patients?page=1", headers=_headers)

    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True
    assert response_json["data"] is not None

    data = response_json["data"]
    assert isinstance(data["items"], list)

    first_item = data["items"][0]
    profile_item = first_item.get("profile")

    for field in PATIENT_FIELDS_ENCRYPTED:
        if profile_item.get(field):
            assert profile_item.get(field) == result.get(field)

    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, [result["patient_user_id"]]
        )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "params",
    [
        {"page": 0, "size": 10},
        {"page": -1, "size": 10},
        {"page": 1, "size": 0},
        {"page": 1, "size": -5},
    ],
)
async def test_get_list_invalid_pagination_input(async_client, _headers, params):
    response = await async_client.get("/v1_0/patients", params=params, headers=_headers)
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    error_data = response.json()
    assert "data" in error_data
    assert isinstance(error_data["data"], list)
    assert len(error_data["data"]) > 0


def get_patient_search_case_payload(**overrides):
    """Create payload for searching patient by field"""
    default_payload = {
        "patient_no": str(uuid.uuid4().int)[:11],
        "last_name": "Test",
        "first_name": "Abcd",
        "last_name_kana": "グエン",
        "first_name_kana": "バン",
    }
    default_payload.update(overrides)
    return default_payload


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "payload, headers_func, expected_status, expected_total",
    [
        (get_patient_search_case_payload(), lambda t, v: get_headers(t, v), 200, 1),
        ({"search": "Test"}, lambda t, v: get_headers(t, v), 200, 1),
        ({"search": "Abcd"}, lambda t, v: get_headers(t, v), 200, 1),
        ({"search": "グエン"}, lambda t, v: get_headers(t, v), 200, 1),
        ({"search": "バン"}, lambda t, v: get_headers(t, v), 200, 1),
        ({"search": ""}, lambda t, v: get_headers(t, v), 200, 1),
        ({"search": "no-match"}, lambda t, v: get_headers(t, v), 200, 0),
    ],
)
async def test_get_list_search_by_fields_and_empty(
    async_client,
    async_tenant_db_session_object,
    _headers,
    valid_headers,
    tenant_uuid,
    payload,
    headers_func,
    expected_status,
    expected_total,
):
    patient_data = get_patient_search_case_payload()

    async with async_tenant_db_session_object.begin():
        patient_id = await unittest_insert_patient(
            async_tenant_db_session_object,
            custom_user_fields={"patient_no": patient_data["patient_no"]},
            custom_profile_fields={
                "last_name": patient_data["last_name"],
                "first_name": patient_data["first_name"],
                "last_name_kana": patient_data["last_name_kana"],
                "first_name_kana": patient_data["first_name_kana"],
            },
        )

    headers = headers_func(tenant_uuid, valid_headers)

    response = await async_client.get("/v1_0/patients", params=payload, headers=headers)

    assert response.status_code == expected_status

    if expected_status == 200:
        data = response.json()["data"]
        assert data["total"] == expected_total
        if expected_total > 0:
            assert data["items"][0]["id"] == patient_id

    async with async_tenant_db_session_object.begin():
        await unittest_remove_patient_by_ids(
            async_tenant_db_session_object, [patient_id]
        )


@pytest.mark.asyncio
async def test_get_list_exception_error(async_client, _headers):
    """Test general exception handling"""
    # Patch the service to throw a generic Exception
    with patch(
        "services.patient_service.PatientService.list_patient",
        side_effect=Exception("Something went wrong"),
    ):
        response = await async_client.get("/v1_0/patients", headers=_headers)

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json()["messageCode"] == 60008
