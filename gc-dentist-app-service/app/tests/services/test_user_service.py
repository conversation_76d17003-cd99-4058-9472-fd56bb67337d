import time
from datetime import datetime

import pytest
from app.services.user_service import UserService
from schemas.requests.user_requests import RolePayloads


@pytest.mark.asyncio
async def test_create_role(async_tenant_db_session_object):
    data = RolePayloads(
        name_json={"en": "admin"},
        sort=int(time.time()) + datetime.now().microsecond,
        role_scope=1,
    )

    service = UserService(session=async_tenant_db_session_object)
    result = await service.create_role(data)

    assert result.id is not None
    assert result.name_json == {"en": "admin"}
