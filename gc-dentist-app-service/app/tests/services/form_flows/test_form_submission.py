from uuid import UUID, uuid4

import pytest
import pytest_asyncio
from schemas.requests.form_submission import (
    FormSubmissionCreate,
    SubmittedForm,
    SubmittedFormFlowData,
    SubmittedFormItem,
    SubmittedFormItemGroup,
)
from services.form_flows.form_submission_services import FormSubmissionService
from sqlalchemy import select, update
from tests.helpers.enums.form_flows import (
    UnittestFormFlowType,
    UnittestFormItemFieldType,
)
from tests.helpers.insert_data.insert_doctor import unittest_insert_doctor
from tests.helpers.insert_data.insert_form import (
    insert_form,
    insert_form_flow,
    insert_form_item,
    insert_form_item_group,
)
from tests.helpers.insert_data.insert_patient import unittest_insert_patient

from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models import FormSubmission


@pytest_asyncio.fixture(scope="function")
async def create_data_form_flow(async_tenant_db_session_object) -> dict:
    async with async_tenant_db_session_object.begin():
        form_flow_uuid = await insert_form_flow(async_tenant_db_session_object)
        form_uuid = await insert_form(async_tenant_db_session_object, form_flow_uuid)
        group_uuid = await insert_form_item_group(
            async_tenant_db_session_object, form_uuid
        )
        item_in_group_uuid = await insert_form_item(
            async_tenant_db_session_object, form_uuid, form_item_group_uuid=group_uuid
        )
        standalone_item_uuid = await insert_form_item(
            async_tenant_db_session_object, form_uuid
        )

    return {
        "form_flow_uuid": form_flow_uuid,
        "form_uuid": form_uuid,
        "group_uuid": group_uuid,
        "item_in_group_uuid": item_in_group_uuid,
        "standalone_item_uuid": standalone_item_uuid,
    }


@pytest.fixture(scope="function")
def valid_submission_data(create_data_form_flow: dict) -> FormSubmissionCreate:
    """
    Creates a valid FormSubmissionCreate object based on the create_data_form_flow fixture.
    This serves as a valid baseline for modification in tests.
    """
    return FormSubmissionCreate(
        form_flow_uuid=create_data_form_flow["form_flow_uuid"],
        doctor_user_id=1,
        patient_user_id=1,
        form_flow_data=SubmittedFormFlowData(
            uuid=create_data_form_flow["form_flow_uuid"],
            flow_name="Valid Flow Name",
            flow_type=UnittestFormFlowType.SURVEY,
            forms=[
                SubmittedForm(
                    uuid=create_data_form_flow["form_uuid"],
                    form_name="Seeded Form",
                    order_index=0,
                    groups=[
                        SubmittedFormItemGroup(
                            uuid=create_data_form_flow["group_uuid"],
                            title="Seeded Group",
                            order_index=0,
                            items=[
                                SubmittedFormItem(
                                    uuid=create_data_form_flow["item_in_group_uuid"],
                                    label="Item In Group",
                                    answer="Answer 1",
                                    field_type=UnittestFormItemFieldType.TEXT,
                                    order_index=0,
                                )
                            ],
                        )
                    ],
                    items=[
                        SubmittedFormItem(
                            uuid=create_data_form_flow["standalone_item_uuid"],
                            label="Standalone Item",
                            answer="Answer 2",
                            field_type=UnittestFormItemFieldType.TEXT,
                            order_index=1,
                        )
                    ],
                )
            ],
        ),
    )


@pytest.mark.asyncio
async def test_validate_submission_structure_pass(
    valid_submission_data, async_tenant_db_session_object
):
    service = FormSubmissionService(async_tenant_db_session_object)

    # Should complete without raising any exception
    await service._validate_submission_structure(valid_submission_data)


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_id, payload_modifier, expected_error_code",
    [
        (
            "form_mismatch",
            lambda data: setattr(data.form_flow_data.forms[0], "uuid", str(uuid4())),
            500103,
        ),
        (
            "group_mismatch",
            lambda data: setattr(
                data.form_flow_data.forms[0].groups[0], "uuid", str(uuid4())
            ),
            500104,
        ),
        (
            "standalone_item_mismatch",
            lambda data: setattr(
                data.form_flow_data.forms[0].items[0], "uuid", str(uuid4())
            ),
            500106,
        ),
        (
            "item_in_group_mismatch",
            lambda data: setattr(
                data.form_flow_data.forms[0].groups[0].items[0], "uuid", str(uuid4())
            ),
            500106,
        ),
    ],
)
async def test_validate_submission_structure_mismatch(
    valid_submission_data,
    async_tenant_db_session_object,
    test_id,
    payload_modifier,
    expected_error_code,
):
    """
    Tests failure cases where a submitted entity UUID does not exist in the real DB.
    """
    # Arrange
    # The valid_submission_data fixture provides a valid baseline
    submission_data = valid_submission_data

    # Modify the baseline to create a specific invalid case
    payload_modifier(submission_data)

    service = FormSubmissionService(async_tenant_db_session_object)

    # Act & Assert
    with pytest.raises(CustomValueError) as exc_info:
        await service._validate_submission_structure(submission_data)

    assert exc_info.value.message_code == expected_error_code


@pytest.mark.asyncio
async def test_validate_create_form_submission_pass(
    valid_submission_data, async_tenant_db_session_object
):
    # Arrange
    # Create the necessary users in the database
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)
        doctor_user_id = await unittest_insert_doctor(async_tenant_db_session_object)

    # Update the submission data with real user IDs
    valid_submission_data.patient_user_id = patient_user_id
    valid_submission_data.doctor_user_id = doctor_user_id

    service = FormSubmissionService(async_tenant_db_session_object)

    # Should complete without raising any exception
    await service._validate_create_form_submission(valid_submission_data)


@pytest.mark.asyncio
async def test_validate_create_form_submission_already_exists(
    valid_submission_data, async_tenant_db_session_object
):
    """
    Tests that a 409 Conflict error is raised if a submission for the
    same patient and form flow already exists.
    """
    # Arrange
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)
        doctor_user_id = await unittest_insert_doctor(async_tenant_db_session_object)

    valid_submission_data.patient_user_id = patient_user_id
    valid_submission_data.doctor_user_id = doctor_user_id

    service = FormSubmissionService(async_tenant_db_session_object)

    # Create the first submission successfully
    await service.create_form_submission(valid_submission_data)

    # Act & Assert: Try to validate for the same submission again
    with pytest.raises(CustomValueError) as exc_info:
        await service._validate_create_form_submission(valid_submission_data)

    assert exc_info.value.status_code == 409
    assert exc_info.value.message_code == 500105


def invalidate_form_flow_uuid(data: FormSubmissionCreate) -> None:
    """Assigns a new, non-existent UUID to the form flow."""
    new_uuid = str(uuid4())
    data.form_flow_uuid = new_uuid
    data.form_flow_data.uuid = new_uuid


def invalidate_patient_id(data: FormSubmissionCreate) -> None:
    """Assigns a non-existent ID to the patient."""
    data.patient_user_id = 99999


def invalidate_doctor_id(data: FormSubmissionCreate) -> None:
    """Assigns a non-existent ID to the doctor."""
    data.doctor_user_id = 99999


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_id, payload_modifier, expected_error_code",
    [
        (
            "form_flow_not_found",
            invalidate_form_flow_uuid,
            500404,
        ),
        (
            "patient_not_found",
            invalidate_patient_id,
            60003,
        ),
        (
            "doctor_not_found",
            invalidate_doctor_id,
            70003,
        ),
    ],
)
async def test_validate_create_form_submission_not_found_errors(
    valid_submission_data,
    async_tenant_db_session_object,
    test_id,
    payload_modifier,
    expected_error_code,
):
    """
    Tests 404 Not Found errors for various missing entities.
    """
    # Arrange
    # Create a valid state first
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)
        doctor_user_id = await unittest_insert_doctor(async_tenant_db_session_object)

    submission_data = valid_submission_data
    submission_data.patient_user_id = patient_user_id
    submission_data.doctor_user_id = doctor_user_id

    # Invalidate a part of the data based on the test case
    payload_modifier(submission_data)

    service = FormSubmissionService(async_tenant_db_session_object)

    # Act & Assert
    with pytest.raises(CustomValueError) as exc_info:
        await service._validate_create_form_submission(submission_data)

    assert exc_info.value.status_code == 400
    assert exc_info.value.message_code == expected_error_code


@pytest.mark.asyncio
async def test_create_form_submission_success(
    valid_submission_data, async_tenant_db_session_object
):
    """
    Tests the successful creation of a form submission,
    verifying data is saved to the DB and a valid UUID is returned.
    """
    # Arrange
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)
        doctor_user_id = await unittest_insert_doctor(async_tenant_db_session_object)

    valid_submission_data.patient_user_id = patient_user_id
    valid_submission_data.doctor_user_id = doctor_user_id

    service = FormSubmissionService(async_tenant_db_session_object)

    # Act
    submission_uuid_str = await service.create_form_submission(valid_submission_data)

    # Assert
    # 1. Check if the returned value is a valid UUID string
    assert isinstance(submission_uuid_str, str)
    assert UUID(submission_uuid_str)

    # 2. Verify that the record was actually created in the database
    async with async_tenant_db_session_object:
        result = await async_tenant_db_session_object.execute(
            select(FormSubmission).where(FormSubmission.uuid == submission_uuid_str)
        )
        created_submission = result.scalar_one_or_none()

    assert created_submission is not None
    assert created_submission.patient_user_id == patient_user_id
    assert (
        str(created_submission.form_flow_uuid) == valid_submission_data.form_flow_uuid
    )


@pytest_asyncio.fixture(scope="function")
async def seeded_form_submission(
    async_tenant_db_session_object,
    valid_submission_data: FormSubmissionCreate,
) -> FormSubmission:
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)
        doctor_user_id = await unittest_insert_doctor(async_tenant_db_session_object)

    submission_payload = valid_submission_data
    submission_payload.patient_user_id = patient_user_id
    submission_payload.doctor_user_id = doctor_user_id

    service = FormSubmissionService(async_tenant_db_session_object)
    submission_uuid = await service.create_form_submission(submission_payload)

    async with async_tenant_db_session_object:
        result = await async_tenant_db_session_object.execute(
            select(FormSubmission).where(FormSubmission.uuid == submission_uuid)
        )
        created_submission = result.scalar_one()
        return created_submission


@pytest.mark.asyncio
async def test_get_detail_form_submission_success(
    seeded_form_submission: FormSubmission, async_tenant_db_session_object
):
    # Arrange
    service = FormSubmissionService(async_tenant_db_session_object)
    submission_uuid = str(seeded_form_submission.uuid)

    # Act
    result = await service.get_detail_form_submission(submission_uuid)

    # Assert
    assert isinstance(result, dict)
    assert str(result["form_flow_uuid"]) == str(seeded_form_submission.form_flow_uuid)
    assert result["patient_user_id"] == seeded_form_submission.patient_user_id
    assert result["doctor_user_id"] == seeded_form_submission.doctor_user_id
    assert result["form_flow_data"] == seeded_form_submission.form_flow_data
    assert result["form_flow_data"] == seeded_form_submission.form_flow_data


@pytest.mark.asyncio
async def test_get_detail_form_submission_not_found(async_tenant_db_session_object):
    """
    Tests that a CustomValueError is raised for a non-existent submission UUID.
    """
    # Arrange
    service = FormSubmissionService(async_tenant_db_session_object)
    non_existent_uuid = str(uuid4())

    # Act & Assert
    with pytest.raises(CustomValueError) as exc_info:
        await service.get_detail_form_submission(non_existent_uuid)

    assert exc_info.value.message_code == 500111


@pytest.mark.asyncio
async def test_get_detail_form_submission_for_inactive_record(
    seeded_form_submission: FormSubmission, async_tenant_db_session_object
):
    """
    Tests that an inactive submission is treated as not found by the service.
    """
    # Arrange
    service = FormSubmissionService(async_tenant_db_session_object)
    submission_uuid = str(seeded_form_submission.uuid)

    # Deactivate the submission in the database
    async with async_tenant_db_session_object.begin():
        await async_tenant_db_session_object.execute(
            update(FormSubmission)
            .where(FormSubmission.uuid == submission_uuid)
            .values(is_active=False)
        )

    # Act & Assert
    with pytest.raises(CustomValueError) as exc_info:
        await service.get_detail_form_submission(submission_uuid)

    assert exc_info.value.message_code == 500111
