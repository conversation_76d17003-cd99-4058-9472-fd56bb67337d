import threading
from typing import Optional

import jwt


class J<PERSON>KSClientManager:
    """Singleton class to manage JWKS client"""

    _instance: Optional[jwt.PyJWKClient] = None
    _lock: threading.Lock = threading.Lock()

    @classmethod
    def get_instance(cls, configuration) -> jwt.PyJWKClient:
        with cls._lock:
            if cls._instance is None:
                url = configuration.AUTH_SERVICE_JWKS_URL
                life_span = configuration.AUTH_SERVICE_JWKS_LIFESPAN
                if configuration.AUTH_SERVICE_JWKS_LIFESPAN is not None:
                    cls._instance = jwt.PyJWKClient(
                        uri=url, cache_jwk_set=True, lifespan=life_span
                    )
                else:
                    cls._instance = jwt.PyJWKClient(url)
        return cls._instance
