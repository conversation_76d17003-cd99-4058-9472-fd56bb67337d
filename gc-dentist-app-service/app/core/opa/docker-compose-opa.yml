version: '2'
services:
  opa:
    image: openpolicyagent/opa:1.3.0
    ports:
    - 8181:8181
    # WARNING: OPA is NOT running with an authorization policy configured. This
    # means that clients can read and write policies in OPA. If you are
    # deploying OPA in an insecure environment, be sure to configure
    # authentication and authorization on the daemon. See the Security page for
    # details: https://www.openpolicyagent.org/docs/security.html.
    command:
    - "run"
    - "--server"
    - "--addr=0.0.0.0:8181"
    - "--log-format=json-pretty"
    - "--set=decision_logs.console=true"
    - "--set=services.nginx.url=http://bundle_server"
    - "--set=bundles.nginx.service=nginx"
    - "--set=bundles.nginx.resource=bundles/bundle.tar.gz"
    depends_on:
    - bundle_server
  bundle_server:
    image: nginx:1.20.0-alpine
    volumes:
    - ./bundles:/usr/share/nginx/html/bundles
