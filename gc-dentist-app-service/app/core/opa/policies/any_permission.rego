package any.authz

default allow = false

# Allow rule for "ALL" condition
allow if {
    input.all_permissions.role_scope == input.requested_permission_key.role_scope
    condition := input.requested_permission_key.condition

    condition == "ALL"
    all_permissions_granted
}

# Allow rule for "ANY" condition
allow if {
    input.all_permissions.role_scope == input.requested_permission_key.role_scope
    condition := input.requested_permission_key.condition

    condition == "ANY"
    some_permission_granted
}

# Check if all requested permissions are granted
all_permissions_granted if {
    not exists_permission_not_granted
}

# Check if any permission is not granted
exists_permission_not_granted if {
    some p in input.requested_permission_key.permissions
    not p_in_all_permissions[p]
}

# Check if at least one permission is granted
some_permission_granted if {
    some p in input.requested_permission_key.permissions
    p_in_all_permissions[p]
}

# Helper function to check if a permission exists in all_permissions
p_in_all_permissions contains p if {
    some p in input.all_permissions.permissions
}

# Sample code to python
#def is_permission_granted(input_data: dict) -> bool:
#    requested = input_data['requested_permission_key']
#    all_perms = input_data['all_permissions']
#
#    if requested['role_scope'] != all_perms['role_scope']:
#        return False
#
#    condition = requested['condition']
#    requested_perms = requested['permissions']
#    granted_perms = set(all_perms['permissions'])
#
#    if condition == 'ALL':
#        return all(p in granted_perms for p in requested_perms)
#
#    elif condition == 'ANY':
#        return any(p in granted_perms for p in requested_perms)
#
#    return False  # default deny
#
#
#
#input_data = {
#    "requested_permission_key": {
#        "role_scope": "admin",
#        "condition": "ALL",  # hoặc "ANY"
#        "permissions": ["read", "write"]
#    },
#    "all_permissions": {
#        "role_scope": "admin",
#        "permissions": ["read", "write", "delete"]
#    }
#}
#
