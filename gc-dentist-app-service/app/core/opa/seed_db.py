import yaml
from account.models.t_role_permission import RolePermission
from core.database.db_mysql import async_db_session
from master.models import Role
from master.models.m_permission import Permission
from sqlalchemy import select, update


def parse_permissions(
    tree: dict, module: str = "", path: list[str] | None = None, role_scope: int = 0
):
    if path is None:
        path = []

    permissions = []
    for key, value in tree.items():
        current_path = [*path, key] if key else path
        if isinstance(value, list):
            full_path = ".".join(filter(None, current_path))
            for action in value:
                permission_key = (
                    f"{module}.{full_path}:{action}"
                    if full_path
                    else f"{module}:{action}"
                )
                permissions.append(
                    Permission(
                        module=module,
                        sub_module=full_path or None,
                        action=action,
                        permission_key=permission_key,
                        role_scope=role_scope,
                        del_flag=False,
                    )
                )
        elif isinstance(value, dict):
            permissions += parse_permissions(value, module, current_path, role_scope)
    return permissions


async def seed_permissions():
    async with async_db_session.begin() as db:
        role_scope_mapping = {
            "SupperAdmin": 1,
            "Corporation": 2,
            "Store": 3,
            "Worker": 4,
        }

        with open("opa/permissions.yaml") as f:
            permissions_data = yaml.safe_load(f)

        # Step 1: Mark all existing permissions as deleted
        await db.execute(update(Permission).values(del_flag=True))

        # Step 2: Load all existing permissions into a map
        result = await db.execute(
            select(Permission.permission_key, Permission.role_scope, Permission.id)
        )
        existing_map = {
            (r["permission_key"], r["role_scope"]): r["id"]
            for r in result.mappings().all()
        }

        perm_objs = []
        for role_name, module_tree in permissions_data.items():
            if not module_tree:
                print(f"[WARN] Tree not found: {role_name}")
                continue

            role_scope = role_scope_mapping.get(role_name)
            if not role_scope:
                print(f"⚠️ Unknown role scope for '{role_name}', skipping...")
                continue
            for module, sub_tree in module_tree.items():
                parsed = parse_permissions(
                    sub_tree, module=module, role_scope=role_scope
                )
                for p in parsed:
                    key = (p.permission_key, p.role_scope)
                    if key in existing_map:
                        await db.execute(
                            update(Permission)
                            .where(Permission.id == existing_map[key])
                            .values(del_flag=False)
                        )
                    else:
                        perm_objs.append(p)

        db.add_all(perm_objs)
        await db.commit()
        print(f"✅ Seeded {len(perm_objs)} new permissions from permissions.yaml")


async def seed_roles_permissions():
    async with async_db_session.begin() as db:
        with open("opa/roles.yaml") as f:
            roles_data = yaml.safe_load(f)

        # Mark all role-permission links as deleted
        await db.execute(update(RolePermission).values(del_flag=True))

        # Load role and permission mappings
        execute_roles = await db.execute(select(Role.id, Role.name_json))
        roles = execute_roles.mappings().all()
        role_map = {r["name_json"]["en_US"]: r["id"] for r in roles}

        execute_permission = await db.execute(
            select(Permission.id, Permission.permission_key).where(
                Permission.del_flag.is_(False)
            )
        )
        permissions = execute_permission.mappings().all()
        perm_map = {p["permission_key"]: p["id"] for p in permissions}

        # Load existing role-permission links
        result = await db.execute(
            select(
                RolePermission.role_id, RolePermission.permission_id, RolePermission.id
            )
        )
        existing_links = {
            (r["role_id"], r["permission_id"]): r["id"] for r in result.mappings().all()
        }

        def match_permission_keys(pattern, all_keys):
            if pattern == "*":
                return list(all_keys.keys())
            elif pattern.endswith(":*"):
                prefix = pattern[:-2]
                return [k for k in all_keys if k.startswith(f"{prefix}")]
            elif ":" in pattern:
                module, action = pattern.split(":", 1)
                return [
                    k
                    for k in all_keys
                    if k.endswith(f":{action}")
                    and k.startswith(f"{module}.")
                    or k == pattern
                ]
            return [pattern]

        for role_name, patterns in roles_data.items():
            if not patterns:
                print(f"[WARN] Pattern not found: {role_name}")
                continue
            role_id = role_map.get(role_name)
            if not role_id:
                print(f"[WARN] Role not found: {role_name}")
                continue

            matched_keys = set()
            for pattern in patterns:
                matched_keys.update(match_permission_keys(pattern, perm_map))

            for key in matched_keys:
                perm_id = perm_map.get(key)
                if not perm_id:
                    print(f"[WARN] Permission not found for key: {key}")
                    continue

                pair_key = (role_id, perm_id)
                if pair_key in existing_links:
                    await db.execute(
                        update(RolePermission)
                        .where(RolePermission.id == existing_links[pair_key])
                        .values(del_flag=False)
                    )
                else:
                    db.add(
                        RolePermission(
                            role_id=role_id, permission_id=perm_id, del_flag=False
                        )
                    )

        await db.commit()
        print("✅ Roles and permissions mapped successfully.")
