SupperAdmin: #Role Scope = 1
  Dashboard:
    Metrics:
      "": ["VIEW"]
    Notifications:
      "": ["VIEW"]
    UserReports:
      "": ["VIEW"]

  Companies:
    ListCompany:
      "": ["VIEW"]
    CreateCompany:
      "": ["CREATE"]
      CreateTicketPlans:
        "": ["CREATE"]
    DetailCompany:
      Overview:
        "": ["VIEW", "CREATE", "UPDATE"]
      AccessManagement:
        "": ["VIEW", "CREATE", "UPDATE"]
        ResendInitPassword:
          "": ["CREATE"]
      PaymentSettings:
        "": ["VIEW", "CREATE", "UPDATE"]
        CalculateNextPayday:
          "": ["VIEW"]
      FunctionSettings:
        "": ["VIEW", "CREATE", "UPDATE"]

  ServiceMonitoring:
    OpeningOffers:
      "": ["VIEW"]
    ClosedOffers:
      "": ["VIEW"]
    WaitingConfirmTimesheets:
      "": ["VIEW"]
    WaitingApprovedTimesheets:
      "": ["VIEW"]
    ApprovedTimesheets:
      "": ["VIEW"]
    ConfirmRewardTimesheets:
      "": ["VIEW"]

  PaymentStatistic:
    InProcessTransfer:
      "": ["VIEW"]
    FailureTransfer:
      "": ["VIEW"]
    SuccessTransfer:
      "": ["VIEW"]

  AttendanceDetails:
    SearchCorporations:
      "": [ "VIEW" ]
    Template:
      "": ["VIEW", "CREATE"]
      Update:
        "": ["UPDATE"]
        Detail:
          "": ["VIEW"]
      Delete:
        "": ["DELETE"]
    TemplateDownload:
      "": ["CREATE"]
      Queue:
        "": ["VIEW"]
    DownloadHistory:
      "": ["VIEW"]


  Notifications:
    ListNotification:
      "": ["VIEW"]
    CreateNotification:
      "": ["CREATE"]
    DetailNotification:
      "": ["VIEW", "UPDATE"]

  GrantedTickets:
    ListGrantedTicket:
      "": ["VIEW"]
      SearchCorporations:
        "": [ "VIEW" ]
    CreateGrantedTicket:
      "": ["CREATE"]
      SendOTPGrantedTicket:
        "": ["CREATE"]
      SearchCorporations:
        "": ["VIEW"]

  UserReports:
    ListUserReport:
      "": ["VIEW"]
    DetailUserReport:
      "": [ "VIEW" ]
      ReplyUserReport:
        "": ["CREATE"]

  IndustryAndOccupations:
    ListIndustryAndOccupation:
      "": ["VIEW"]
    UpdateIndustryAndOccupation:
      "": ["VIEW"]
      AddOccupation:
        "": ["VIEW", "UPDATE"]
      AddSkill:
        "": ["VIEW", "UPDATE"]

  Settings:
    Versioning:
      "": ["VIEW", "UPDATE"]
    LambdaQueue:
      "": ["VIEW", "CREATE"]
    AppVersionRequired:
      "": ["VIEW", "CREATE", "DELETE"]

  QuestionnaireManagement:
    ListQuestionnaireManagement:
      "": ["VIEW"]
      SearchQuestionnaireTags:
        "": ["VIEW"]
    CreateQuestionnaireManagement:
      "": ["CREATE"]
      SearchCorporations:
        "": [ "VIEW" ]
      SearchQuestionnaireTags:
        "": [ "VIEW" ]
    DetailQuestionnaireManagement:
      "": ["VIEW"]
      SearchCorporations:
        "": ["VIEW"]
      UpdateQuestionnaireManagement:
        "": ["UPDATE"]
        SearchCorporations:
          "": [ "VIEW" ]
        SearchQuestionnaireTags:
          "": [ "VIEW" ]

  Login:
    ForgotPassword:
      "": ["VIEW"]
    ChangePassword:
      "": ["VIEW"]

  MasterData:
    View:
      "": ["VIEW"]
    ClearCache:
      "": ["DELETE"]

  Bank:
    Info:
      "": ["UPDATE"]

#  Compensation Adjustment or Worker Compensation
  AdjustmentBilling:
    View:
      "": ["VIEW"]
    Billing:
      "": ["VIEW"]
    LookupBilling:
      "": ["VIEW"]
    Reason:
      "": ["VIEW"]
    GetWorkerWallet:
      "": ["VIEW"]
    GenerateOTP:
      "": ["CREATE"]
    CreateCompensationBilling:
      "": ["CREATE"]
    LookupOffer:
      "": ["VIEW"]
    CreateCompensationConfirmedRewardOffer:
      "": ["CREATE"]
    Detail:
      "": ["VIEW"]

  Encrypt:
    Worker:
      "": ["UPDATE"]

  Decrypt:
    Worker:
      "": [ "UPDATE" ]

  BirthdayHash:
    Worker:
      "": ["UPDATE"]


  Worker:
    Reactive:
      "": ["UPDATE"]
    SetPassword:
      "": ["UPDATE"]

  Corporation:
    SearchCorporations:
      "": ["VIEW"]

  Authentik:
    SyncData:
      "": ["CREATE"]

  Migrate:
    "": ["CREATE"]

  Migration:
    "": ["CREATE"]

  PaymentService:
    SyncStatus:
      "": ["UPDATE"]


Corporation: #Role Scope = 2

Store:  #Role Scope = 3
