from typing import Optional

from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from enums.opa_enum import OPACondition
from fastapi import Depends, Request, status

from gc_dentist_shared.core.common.http_request import <PERSON>ton<PERSON>e<PERSON><PERSON><PERSON>
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log


class RolePermissionDependWithOPA:
    def __init__(
        self, permission_keys: list[str], condition: str = OPACondition.ANY.value
    ):
        self.permission_keys = permission_keys
        self.condition = condition

    async def __call__(self, request: Request):
        """
        Validates user permissions for a requested action using Open Policy Agent (OPA).

        This method supports two types of permission checks:
        1. ALL Condition: Requires ALL requested permissions to match
        2. ANY Condition: Requires AT LEAST ONE requested permission to match

        Parameters:
            permission_keys (list): List of permission keys to check
            condition (str, optional): Permission matching condition
                - 'ALL': All requested permissions must exist (default)
                - 'ANY': At least one requested permission must exist

        Examples:
            # ANY Condition (Strict Match)
            Input: {
                "input": {
                    "all_permissions": {
                        "role_scope": 1,
                        "permissions": [
                            "Dashboard:VIEW",
                            "Companies.ListCompany:VIEW",
                            "Companies.CreateCompany:CREATE"
                        ]
                    },
                    "requested_permission_key": {
                        "role_scope": 1,
                        "permissions": [
                            "Dashboard:VIEW",
                            "Dashboard:CREATE"
                        ],
                        "condition": "ALL"
                    }
                }
            }
            # Result: False (not all requested permissions exist)

            # OR Condition (Relaxed Match)
            Input: {
                "input": {
                    "all_permissions": {
                        "role_scope": 1,
                        "permissions": [
                            "Dashboard:VIEW",
                            "Companies.ListCompany:VIEW",
                            "Companies.CreateCompany:CREATE"
                        ]
                    },
                    "requested_permission_key": {
                        "role_scope": 1,
                        "permissions": [
                            "Dashboard:VIEW",
                            "Dashboard:CREATE"
                        ],
                        "condition": "ANY"
                    }
                }
            }
            # Result: True (at least one requested permission exists)

        Returns:
            dict: OPA decision result with 'decision_id' and 'result' boolean
            - 'result': True if permission is granted, False otherwise
        """

        if not self.permission_keys:
            raise CustomValueError(
                status_code=status.HTTP_403_FORBIDDEN,
                message_code=CustomMessageCode.FORBIDDEN_ERROR.code,
                message=CustomMessageCode.FORBIDDEN_ERROR.title,
            )

        permission_request_data = await self._build_permission_request(
            role_scope=request.user.role_scope, account_id=request.user.id
        )

        response = await self._check_permissions_with_opa(permission_request_data)
        if not response.get("result", False):  # Simplified validation condition
            raise CustomValueError(
                status_code=status.HTTP_403_FORBIDDEN,
                message_code=CustomMessageCode.FORBIDDEN_ERROR.code,
                message=CustomMessageCode.FORBIDDEN_ERROR.title,
            )

    async def _build_permission_request(self, role_scope: int, account_id: int):
        db_session = await TenantDatabase().get_instance_tenant_db()
        async with db_session:
            # role_permission = await dao_role.role_get_by_account_id(
            #     db=db, account_id=account_id
            # ) -> Get role permission for account
            role_permission = {
                "role_id": 1,
                "role_scope": 1,
            }

            if not role_permission or not role_permission.get("role_id"):
                raise CustomValueError(
                    status_code=status.HTTP_403_FORBIDDEN,
                    message_code=CustomMessageCode.FORBIDDEN_ERROR.code,
                    message=CustomMessageCode.FORBIDDEN_ERROR.title,
                )

            # permissions = await RolePermissionDao.get_all_permission_key(
            #     db,
            #     role_id=role_permission.role_id,
            #     role_scope=role_permission.role_scope,
            # ) -> Get list permission for account
            permissions = [
                "Dashboard:VIEW",
                "Companies.ListCompany:VIEW",
                "Companies.CreateCompany:CREATE",
            ]

            all_permissions = {
                "role_scope": role_permission.get("role_scope"),
                "permissions": permissions,
            }
            requested_permission_key = {
                "role_scope": role_scope,
                "permissions": [
                    permission_key for permission_key in self.permission_keys
                ],
                "condition": self.condition,
            }

            return {
                "input": {
                    "all_permissions": all_permissions,
                    "requested_permission_key": requested_permission_key,
                }
            }

    async def _check_permissions_with_opa(self, obj_request) -> dict:
        """
        :param obj_request:
        :return: {
            "decision_id": "432391d2-9271-4f20-b5ec-e56c15f32221",
            "result": true
        }
        """

        opa_service_base_url = "http://localhost:8181/"
        opa_service_api_version = "v1/data/"
        opa_noda_permission = "noda/authz/allow"
        url = opa_service_base_url + opa_service_api_version + opa_noda_permission

        api_client = await SingletonRequestApi.get_instance()

        status_code, response_data = await api_client.request(
            method="POST",
            url=url,
            json=obj_request,
        )

        if status_code != status.HTTP_200_OK or not response_data:
            log.error(f"❌  Call Opa Error: str{response_data}")
            raise CustomValueError(
                message_code=CustomMessageCode.CALL_OPA_ERROR.code,
                message=CustomMessageCode.CALL_OPA_ERROR.title,
            )

        return response_data


def DependsPermissionWithOPA(
    permission_keys: Optional[list[str]] = None,
    condition: str | None = OPACondition.ANY.value,
):
    if permission_keys is None:
        permission_keys = []
    return Depends(
        RolePermissionDependWithOPA(
            [p.value if hasattr(p, "value") else p for p in permission_keys],
            condition=condition,
        )
    )
