from configuration.settings import configuration
from core.common.jwks_client_manager import JW<PERSON><PERSON>lient<PERSON>anager
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON>2<PERSON><PERSON><PERSON><PERSON>earer

from gc_dentist_shared.core.common.jwt_token import decode_jwt_token
from gc_dentist_shared.core.logger.config import log

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


def _unauthorized(detail: str = "Invalid token") -> HTTPException:
    return HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail=detail,
        headers={"WWW-Authenticate": "Bearer"},
    )


async def get_current_user_claims(token: str = Depends(oauth2_scheme)):
    try:
        # 1) Find public key via JWKS
        jwks_client = JWKSClientManager.get_instance(configuration)
        signing_key = jwks_client.get_signing_key_from_jwt(token)

        # 2) Verify token
        payload = decode_jwt_token(signing_key.key, configuration.JWT_ALGORITHM, token)
        if not payload:
            raise _unauthorized()

        return payload

    except HTTPException:
        raise
    except Exception as e:
        log.error(f"❌ Validate token failed: {e}")
        raise _unauthorized()
