import uuid
from datetime import date, datetime, timezone
from typing import Optional

from core.constants import DOCUMENT_MANAGEMENT_DEFAULT_VERSION
from core.messages import CustomMessageCode
from fastapi_pagination.ext.sqlalchemy import paginate
from schemas.requests.document_management_schema import (
    DocumentPreviewQueryParams,
    DocumentQueryParams,
)
from schemas.requests.document_managerments_schema import (
    DocumentManagementCreateSchema,
    DocumentManagementUpdateSchema,
)
from schemas.responses.document_management_schema import GetDocumentSchema
from schemas.responses.document_managerments_schema import (
    DocumentManagementUpdateResponse,
)
from sqlalchemy import func, select, update
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.enums.document import DocumentDataType, DocumentStatus
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models import (
    DocumentGroup,
    DocumentManagement,
    DocumentManagementVersion,
    MedicalHistory,
    PatientUser,
)


class DocumentManagementService:
    def __init__(self, session: AsyncSession):
        self.session = session

    # region Public Methods
    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_document",
    )
    async def get_document(self, patient_user_id: int, document_id: int):
        query = select(
            DocumentManagement.id,
            DocumentManagement.patient_user_id,
            DocumentManagement.name,
            DocumentManagement.document_group_id,
            DocumentManagement.data_type,
            DocumentManagement.document_data,
            DocumentManagement.examination_date,
            DocumentManagement.medical_history_id,
            DocumentManagement.version_uuid,
            DocumentManagement.display_mode,
        ).where(
            DocumentManagement.id == document_id,
            DocumentManagement.patient_user_id == patient_user_id,
            DocumentManagement.status == DocumentStatus.ACTIVATED.value,
        )

        async with self.session:
            result = await self.session.execute(query)
            row = result.mappings().one_or_none()

        if not row:
            raise CustomValueError(
                message_code=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.code,
                message=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.title,
            )

        return GetDocumentSchema(**row)

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_list_document",
    )
    async def get_list(
        self, patient_user_id: int, filters: Optional[DocumentQueryParams] = None
    ):
        conditions = [
            DocumentManagement.patient_user_id == patient_user_id,
            DocumentManagement.status == DocumentStatus.ACTIVATED.value,
        ]
        grouped_query = (
            select(
                func.date(DocumentManagement.examination_date).label(
                    "examination_date"
                ),
                func.json_agg(
                    func.json_build_object(
                        "id",
                        DocumentManagement.id,
                        "name",
                        DocumentManagement.name,
                        "patient_user_id",
                        DocumentManagement.patient_user_id,
                        "document_group_id",
                        DocumentManagement.document_group_id,
                        "data_type",
                        DocumentManagement.data_type,
                        "document_data",
                        DocumentManagement.document_data,
                        "medical_history_id",
                        DocumentManagement.medical_history_id,
                        "examination_date",
                        DocumentManagement.examination_date,
                        "version_uuid",
                        DocumentManagement.version_uuid,
                        "display_mode",
                        DocumentManagement.display_mode,
                        "updated_at",
                        DocumentManagement.updated_at,
                    )
                ).label("list_document"),
            )
            .where(*conditions)
            .group_by(func.date(DocumentManagement.examination_date))
            .order_by(func.date(DocumentManagement.examination_date).desc())
        )

        result = await paginate(
            self.session,
            grouped_query,
            unique=False,
        )

        for item in result.items:
            item.list_document.sort(
                key=lambda d: (d.updated_at is not None, d.updated_at), reverse=True
            )

        return result

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_preview_documents",
    )
    async def get_preview_documents(
        self, patient_user_id: int, filters: DocumentPreviewQueryParams
    ):
        conditions = [
            DocumentManagement.patient_user_id == patient_user_id,
            DocumentManagement.status == DocumentStatus.ACTIVATED.value,
        ]

        conditions_map = {
            "document_ids": lambda v: DocumentManagement.id.in_(v),
            "examination_date": lambda v: DocumentManagement.examination_date == v,
        }

        conditions.extend(
            builder(value)
            for key, builder in conditions_map.items()
            if (value := getattr(filters, key))
        )

        query = (
            select(
                DocumentManagement.id,
                DocumentManagement.patient_user_id,
                DocumentManagement.name,
                DocumentManagement.document_group_id,
                DocumentManagement.data_type,
                DocumentManagement.document_data,
                DocumentManagement.examination_date,
                DocumentManagement.medical_history_id,
                DocumentManagement.version_uuid,
                DocumentManagement.display_mode,
                DocumentManagement.updated_at,
            )
            .where(*conditions)
            .order_by(func.date(DocumentManagement.examination_date).desc())
        )

        async with self.session:
            result = await self.session.execute(query)
            rows = result.mappings().all()

        return [GetDocumentSchema(**row).model_dump(mode="json") for row in rows]

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_document_management",
    )
    async def create_document_management(
        self, create_obj: DocumentManagementCreateSchema
    ):
        async with self.session.begin():
            await self._validate_before_create_document(create_obj, self.session)

            medical_history_data = await self._get_medical_history_by_date(
                create_obj.examination_date, create_obj.patient_user_id, self.session
            )

            document_data = []
            for data in create_obj.document_data:
                document_data.append({data.file_index: data.file_key})

            # Create Document Management
            document_management = await self._create_document_management(
                db_session=self.session,
                obj=create_obj,
                medical_history_id=medical_history_data.get("medical_history_id", None),
                examination_date=medical_history_data.get("examination_date", None),
                document_data=document_data,
                create_obj=create_obj,
            )

            return document_management.id

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="update_document_management",
    )
    async def update_document_management(
        self, document_management_id: int, update_obj: DocumentManagementUpdateSchema
    ) -> DocumentManagementUpdateResponse:
        async with self.session.begin():
            await self._validate_before_update_or_delete_document(
                document_management_id, self.session
            )

            update_fields = {"data_type": DocumentDataType.EDITED.value}
            if update_obj.examination_date:
                medical_history_data = await self._get_medical_history_by_date(
                    examination_date=update_obj.examination_date,
                    patient_user_id=update_obj.patient_user_id,
                    db_session=self.session,
                )
                update_fields.update(medical_history_data)

            document_management = await self.session.execute(
                select(DocumentManagement).where(
                    DocumentManagement.id == document_management_id
                )
            )
            document_management = document_management.scalar_one()

            await self._check_and_create_original_document_version(
                document_management, self.session
            )

            self._update_document_fields(document_management, update_obj, update_fields)

            document_management.version_uuid = str(uuid.uuid4())

            await self.session.flush()
            await self.session.refresh(document_management)

            return DocumentManagementUpdateResponse.model_validate(document_management)

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="delete_document_management",
    )
    async def delete_document_management(self, document_management_id: int):
        async with self.session.begin():
            await self._validate_before_update_or_delete_document(
                document_management_id, self.session
            )

            await self.session.execute(
                update(DocumentManagement)
                .where(DocumentManagement.id == document_management_id)
                .values(
                    status=DocumentStatus.INACTIVATED.value,
                    deleted_at=datetime.now(timezone.utc),
                )
            )

    # endregion Public Methods

    # region Private Methods
    async def _validate_before_create_document(self, obj, db_session: AsyncSession):
        stmt_patient = select(PatientUser).where(
            PatientUser.id == obj.patient_user_id, PatientUser.status.is_(True)
        )
        result_patient = await db_session.execute(stmt_patient)
        patient_user = result_patient.scalar_one_or_none()
        if not patient_user:
            raise CustomValueError(
                message_code=CustomMessageCode.PATIENT_NOT_FOUND.code,
                message=CustomMessageCode.PATIENT_NOT_FOUND.title,
            )

        stmt_document_group = select(DocumentGroup).where(
            DocumentGroup.id == obj.document_group_id,
            DocumentGroup.deleted_at.is_(None),
        )
        result_document_group = await db_session.execute(stmt_document_group)
        document_group = result_document_group.scalar_one_or_none()
        if not document_group:
            raise CustomValueError(
                message_code=CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.code,
                message=CustomMessageCode.DOCUMENT_GROUP_NOT_FOUND.title,
            )

    async def _get_medical_history_by_date(
        self,
        examination_date: Optional[str],
        patient_user_id: int,
        db_session: AsyncSession,
    ):
        conditions = [MedicalHistory.patient_user_id == patient_user_id]
        exam_date = None

        if examination_date:
            exam_date = datetime.strptime(examination_date, "%Y-%m-%d").date()
            conditions.append(
                func.date(MedicalHistory.visit_start_datetime) == exam_date
            )

        stmt = (
            select(MedicalHistory)
            .where(*conditions)
            .order_by(MedicalHistory.visit_start_datetime.desc())
        )
        result = await db_session.execute(stmt)
        medical_history = result.scalar_one_or_none()

        final_exam_date = exam_date or (
            medical_history.visit_start_datetime.date() if medical_history else None
        )

        return {
            "medical_history_id": getattr(medical_history, "id", None),
            "examination_date": final_exam_date,
        }

    async def _create_document_management(
        self,
        obj,
        examination_date: date,
        document_data,
        create_obj,
        db_session: AsyncSession,
        medical_history_id: Optional[int] = None,
    ):
        version_uuid = str(uuid.uuid4())
        document_management = DocumentManagement(
            patient_user_id=obj.patient_user_id,
            name=obj.name,
            status=obj.status,
            data_type=obj.data_type,
            document_data=document_data,
            examination_date=examination_date,
            medical_history_id=medical_history_id,
            version_uuid=version_uuid,
            display_mode=create_obj.display_mode,
            extra_data=create_obj.extra_data,
            document_group_id=create_obj.document_group_id,
        )
        db_session.add(document_management)
        await db_session.flush()
        await db_session.refresh(document_management)
        return document_management

    async def _validate_before_update_or_delete_document(
        self, document_management_id: int, db_session: AsyncSession
    ):
        stmt = select(DocumentManagement).where(
            DocumentManagement.id == document_management_id,
            DocumentManagement.status == DocumentStatus.ACTIVATED.value,
        )
        result = await db_session.execute(stmt)
        document_management = result.scalar_one_or_none()
        if not document_management:
            raise CustomValueError(
                message_code=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.code,
                message=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.title,
            )

    async def _check_and_create_original_document_version(
        self, document_management, db_session: AsyncSession
    ):
        # Check if version exists
        document_version_result = await db_session.execute(
            select(DocumentManagementVersion).where(
                DocumentManagementVersion.document_management_id
                == document_management.id
            )
        )
        has_document_version = document_version_result.scalar_one_or_none()

        # Create version if not exists
        if not has_document_version:
            document_management_version = DocumentManagementVersion(
                patient_user_id=document_management.patient_user_id,
                document_management_id=document_management.id,
                document_data=document_management.document_data,
                version=DOCUMENT_MANAGEMENT_DEFAULT_VERSION,
                data_type=DocumentDataType.ORIGINAL.value,
                version_uuid=document_management.version_uuid,
            )
            db_session.add(document_management_version)

    def _update_document_fields(self, document_management, update_obj, update_fields):
        update_data = update_obj.model_dump(exclude_unset=True)
        update_data.update(update_fields)

        for field, value in update_data.items():
            setattr(document_management, field, value)

    # endregion Private Methods # noqa: E305
