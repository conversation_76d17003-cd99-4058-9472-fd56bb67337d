from configuration.settings import configuration
from core.constants import DOCTOR_FIELDS_ENCRYPTED, DOCTOR_FIELDS_HASHED
from core.messages import CustomMessageCode
from fastapi import status
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from schemas.requests.doctor_user_schema import (
    CreateDoctorPayloads,
    UpdateDoctorPayloads,
)
from schemas.responses.doctor_user_schema import DoctorListSche<PERSON>, GetDoctorSchema
from sqlalchemy import func, select, update
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common import utils
from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models.doctor_profiles import <PERSON><PERSON><PERSON><PERSON><PERSON>
from gc_dentist_shared.tenant_models.doctor_users import DoctorUser


class DoctorService:
    def __init__(self, session: AsyncSession):
        self.session = session
        self.aes_gcm = AesGCMRotation(configuration)

    async def validate_doctor_user(self, doctor_user_id: int) -> DoctorUser:
        """
        Validate if the doctor user exists and is active.
        Raise CustomValueError if not found.
        """
        async with self.session.begin():
            query = (
                select(DoctorUser.id)
                .join(DoctorProfile, DoctorUser.id == DoctorProfile.doctor_user_id)
                .where(DoctorUser.id == doctor_user_id, DoctorUser.status.is_(True))
            )
            result = await self.session.execute(query)
            doctor_user = result.scalar_one_or_none()
            if not doctor_user:
                raise CustomValueError(
                    message_code=CustomMessageCode.DOCTOR_NOT_FOUND.code,
                    message=CustomMessageCode.DOCTOR_NOT_FOUND.title,
                )
            return doctor_user

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_doctor",
    )
    async def create(self, data: CreateDoctorPayloads) -> bool:
        try:
            async with self.session.begin():
                doctor = await self._create_doctor_user(data)
                self._create_doctor_profile(data, doctor.id)
            return doctor.id
        except Exception as e:
            log.error(f"❌ Error create doctor users: {e}")
            raise e

    async def _create_doctor_user(self, data) -> DoctorUser:
        doctor = DoctorUser(
            username=data.email,
            password=utils.generate_password(),
            required_change_password=True,
            status=True,
        )

        self.session.add(doctor)
        await self.session.flush()

        return doctor

    def _create_doctor_profile(
        self, doctor_profile, doctor_user_id: int
    ) -> DoctorProfile:
        data_profile = doctor_profile.model_dump(mode="json")
        encrypted_result = AesGCMRotation(
            configuration
        ).encrypt_and_hash_selected_fields(
            data_profile, DOCTOR_FIELDS_ENCRYPTED, DOCTOR_FIELDS_HASHED
        )

        profile_model = DoctorProfile(
            doctor_user_id=doctor_user_id,
            **encrypted_result,
        )

        self.session.add(profile_model)

        return profile_model

    async def validate_create_doctor(self, data: CreateDoctorPayloads):
        exits_phone = await self._check_exists_phone(data.country_code, data.phone)
        if exits_phone:
            raise CustomValueError(
                status_code=status.HTTP_400_BAD_REQUEST,
                message_code=CustomMessageCode.DOCTOR_EXISTS_PHONE.code,
                message=CustomMessageCode.DOCTOR_EXISTS_PHONE.title,
            )

        exists_username = await self._check_exists_email(data.email)

        if exists_username:
            raise CustomValueError(
                status_code=status.HTTP_400_BAD_REQUEST,
                message_code=CustomMessageCode.DOCTOR_EXISTS_EMAIL.code,
                message=CustomMessageCode.DOCTOR_EXISTS_EMAIL.title,
            )

        return True

    async def _check_exists_phone(self, country_code: str, phone: str):
        phone_hash = self.aes_gcm.sha256_hash(phone)
        stmt = select(DoctorProfile.id).where(
            DoctorProfile.phone_hash == phone_hash,
            DoctorProfile.country_code == country_code,
        )
        async with self.session:
            result = await self.session.execute(stmt)

            return result.scalar_one_or_none() is not None

    async def _check_exists_email(self, email: str):
        email_hash = self.aes_gcm.sha256_hash(email)
        stmt = select(DoctorProfile.id).where(DoctorProfile.email_hash == email_hash)
        async with self.session:
            result = await self.session.execute(stmt)

            return result.scalar_one_or_none() is not None

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="get_doctor_profile",
    )
    async def get_doctor_profile(self, doctor_id: int) -> GetDoctorSchema:
        profile = func.json_build_object(
            "last_name",
            DoctorProfile.last_name,
            "first_name",
            DoctorProfile.first_name,
            "last_name_kana",
            DoctorProfile.last_name_kana,
            "first_name_kana",
            DoctorProfile.first_name_kana,
            "country_code",
            DoctorProfile.country_code,
            "phone",
            DoctorProfile.phone,
            "email",
            DoctorProfile.email,
            "gender",
            DoctorProfile.gender,
            "date_of_birth",
            DoctorProfile.date_of_birth,
            "prefecture_id",
            DoctorProfile.prefecture_id,
            "postal_code",
            DoctorProfile.postal_code,
            "address_1",
            DoctorProfile.address_1,
            "address_2",
            DoctorProfile.address_2,
            "address_3",
            DoctorProfile.address_3,
            "order_index",
            DoctorProfile.order_index,
        ).label("profile")

        query = (
            select(
                DoctorUser.username,
                DoctorUser.uuid,
                profile,
            )
            .join(DoctorUser, DoctorUser.id == DoctorProfile.doctor_user_id)
            .where(DoctorUser.id == doctor_id)
        )

        async with self.session:
            result = await self.session.execute(query)
            row = result.mappings().one_or_none()

        if not row:
            raise CustomValueError(
                message_code=CustomMessageCode.DOCTOR_NOT_FOUND.code,
                message=CustomMessageCode.DOCTOR_NOT_FOUND.title,
            )

        return GetDoctorSchema(**row)

    async def list_doctor(self) -> Page[DoctorListSchema]:
        doctor_profile_json = func.json_build_object(
            "last_name",
            DoctorProfile.last_name,
            "first_name",
            DoctorProfile.first_name,
            "last_name_kana",
            DoctorProfile.last_name_kana,
            "first_name_kana",
            DoctorProfile.first_name_kana,
            "gender",
            DoctorProfile.gender,
            "date_of_birth",
            DoctorProfile.date_of_birth,
            "country_code",
            DoctorProfile.country_code,
            "phone",
            DoctorProfile.phone,
            "email",
            DoctorProfile.email,
        ).label("profile")

        query = (
            select(DoctorUser.id, DoctorUser.username, doctor_profile_json)
            .select_from(DoctorUser)
            .join(DoctorProfile, DoctorUser.id == DoctorProfile.doctor_user_id)
            .where(DoctorUser.status.is_(True))
            .order_by(DoctorProfile.order_index.asc())
            .order_by(DoctorUser.created_at.asc())
        )

        return await paginate(self.session, query, unique=False)

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="update_doctor_profile",
    )
    async def update_doctor_profile(
        self, doctor_user_id: int, data: UpdateDoctorPayloads
    ) -> int:
        await self.validate_doctor_user(doctor_user_id)

        update_fields = data.model_dump(exclude_unset=True, mode="json")
        if not update_fields:
            return doctor_user_id

        email = update_fields.pop("email", None)
        if email:
            async with self.session.begin():
                query = select(DoctorUser).where(
                    DoctorUser.username == email, DoctorUser.id != doctor_user_id
                )
                result = await self.session.execute(query)
                existed_doctor = result.scalar_one_or_none()

                if existed_doctor is not None:
                    raise CustomValueError(
                        message_code=CustomMessageCode.DOCTOR_EXISTS_EMAIL.code,
                        message=CustomMessageCode.DOCTOR_EXISTS_EMAIL.title,
                    )
                update_fields["email"] = email

        async with self.session.begin():
            if email:
                stmt_user = (
                    update(DoctorUser)
                    .where(DoctorUser.id == doctor_user_id)
                    .values(username=email)
                )
                await self.session.execute(stmt_user)

            encrypted_result = AesGCMRotation(
                configuration
            ).encrypt_and_hash_selected_fields(
                update_fields, DOCTOR_FIELDS_ENCRYPTED, DOCTOR_FIELDS_HASHED
            )

            stmt_profile = (
                update(DoctorProfile)
                .where(DoctorProfile.doctor_user_id == doctor_user_id)
                .values(
                    **encrypted_result,
                )
            )
            await self.session.execute(stmt_profile)

        return doctor_user_id
