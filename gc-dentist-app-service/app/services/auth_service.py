from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from configuration.settings import configuration
from core.constants import FIELDS_ENCRYPTED
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase, TenantDatabase
from schemas.requests.auth_schema import LoginRequestSchema, TenantInfoSchema
from schemas.responses.auth_schema import (
    ClinicInfoResponseSchema,
    DoctorProfileResponseSchema,
    LoginResponseSchema,
)
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import TenantClinic
from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.exception_handler.custom_exception import (
    ExceptionNotFoundTenantError,
)
from gc_dentist_shared.tenant_models import ClinicIn<PERSON>, DoctorPro<PERSON>le, DoctorUser


class AuthServices:
    async def get_tenant_with_tenant_slug(self, tenant_slug: str) -> TenantInfoSchema:
        db_session = await CentralDatabase().get_instance_db()
        async with db_session.begin():
            result = await db_session.execute(
                select(
                    TenantClinic.db_name,
                    TenantClinic.tenant_uuid,
                ).where(TenantClinic.tenant_slug == tenant_slug)
            )
            row = result.one_or_none()
            if not row:
                raise ExceptionNotFoundTenantError(
                    CustomMessageCode.TENANT_NOT_FOUND.title
                )

            db_name, tenant_uuid = row
            return TenantInfoSchema(db_name=db_name, tenant_uuid=str(tenant_uuid))

    async def login(
        self, tenant_slug: str, obj: LoginRequestSchema
    ) -> LoginResponseSchema:
        if not tenant_slug:
            raise ExceptionNotFoundTenantError(
                CustomMessageCode.X_TENANT_SLUG_IS_REQUIRED.title
            )

        tenant = await self.get_tenant_with_tenant_slug(tenant_slug)
        token = set_current_db_name(tenant.db_name)
        try:
            return await self.authenticate_user(tenant.tenant_uuid, obj)
        except ValueError as e:
            raise ValueError(str(e))
        except Exception as e:
            raise Exception(f"Authentication failed: {str(e)}")
        finally:
            reset_current_db_name(token)

    async def authenticate_user(
        self, tenant_uuid: str, obj: LoginRequestSchema
    ) -> LoginResponseSchema:
        session = await TenantDatabase.get_instance_tenant_db()
        async with session.begin():
            result = await session.execute(
                select(DoctorUser).where(
                    DoctorUser.username == obj.username,
                    DoctorUser.status.is_(True),
                )
            )
            user = result.scalar_one_or_none()
            if not user or not user.validate_password(obj.password, tenant_uuid):
                raise ValueError(CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.title)
        # TODO - Implement user authentication logic

        return LoginResponseSchema(
            tenant_uuid=tenant_uuid,
            token="1",  # noqa: S106
            refresh_token="1",  # noqa: S106
        )

    async def get_doctor_profile(
        self, db_session: AsyncSession, user_id: int, clinic_uuid: str
    ) -> DoctorProfileResponseSchema:
        async with db_session.begin():
            result = await db_session.execute(
                select(DoctorProfile).where(
                    DoctorProfile.doctor_user_id == user_id,
                )
            )

            user = result.scalar_one_or_none()
            if not user:
                raise ValueError(CustomMessageCode.USER_NOT_FOUND.title)

            result = await db_session.execute(
                select(ClinicInformation).where(
                    ClinicInformation.clinic_uuid == clinic_uuid,
                    ClinicInformation.is_active.is_(True),
                )
            )
            clinic = result.scalar_one_or_none()
            if not clinic:
                raise ValueError(CustomMessageCode.CLINIC_INFO_NOT_FOUND.title)

            doctor_data = user.__dict__.copy()
            doctor_data["id"] = user_id

            doctor_data = AesGCMRotation(configuration).decrypt_selected_fields(
                data=doctor_data,
                fields=FIELDS_ENCRYPTED,
            )

            return DoctorProfileResponseSchema(
                **doctor_data,
                clinic_info=ClinicInfoResponseSchema(**clinic.__dict__),
            )
