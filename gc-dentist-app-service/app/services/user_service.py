# import uuid

# from config.logger.config import log
# from core.common.aes_gcm import AesGCMRotation
# from db.models.client_management import ClientManagement
# from schemas.client_requests import (ClientManagementCreate,
#                                      ClientManagementUpdate)
# from sqlalchemy import and_, func, select, update
from sqlalchemy.exc import DB<PERSON>IError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.tenant_models.roles import Role

# from sqlalchemy.future import select
# from sqlalchemy.exc import IntegrityError
# from db.models.user import User
# from schemas.user_requests import RegisterRequest


class UserService:
    def __init__(self, session: AsyncSession):
        self.session = session

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_role",
    )
    async def create_role(self, data: Role):
        role = Role(**data.model_dump())
        async with self.session.begin():
            self.session.add(role)
            await self.session.flush()
            await self.session.refresh(role)
            return role
