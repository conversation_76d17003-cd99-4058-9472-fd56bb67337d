from typing import Optional

from pydantic import BaseModel, Field

from gc_dentist_shared.core.common.utils import ValidateDateString
from gc_dentist_shared.core.enums.medical_device import MedicalDeviceType


class ImagePathSchema(BaseModel):
    original: str
    thumbnail: Optional[str] = None
    order_index: int


class FiDataSchema(BaseModel):
    type: MedicalDeviceType
    id: str
    patient_no: int
    examination_date: ValidateDateString


class BfaDataSchema(BaseModel):
    business_number: str
    fi_data: FiDataSchema
    auto_cleaning: str
    area_total: float
    area_left: float
    area_right: float
    ave: float
    ave_left: float
    ave_right: float
    max_total: float
    max_left: float
    max_right: float
    force_total: float
    force_left: float
    force_right: float
    comment: str
    image_file_paths: Optional[list[ImagePathSchema]] = Field(
        None,
        max_length=1,
        description="A list of image paths, with a maximum of one item.",
    )


class EmgDataSchema(BaseModel):
    business_number: str
    fi_data: FiDataSchema
    start_time: str
    end_time: str
    total_time: str
    max_peak: float
    base_peak: float
    total_clenching: int
    clenching_per_hour: float
    burst_total: float
    burst_total_dur: str
    burst_total_ave: str
    burst_per_hour: float
    burst_total_duration_per_hour: str
    clenching_strength_ave: float
    image_file_paths: Optional[list[ImagePathSchema]] = Field(
        None,
        max_length=1,
        description="A list of image paths, with a maximum of one item.",
    )


class MvtDataSchema(BaseModel):
    business_number: str
    fi_data: FiDataSchema
    image_file_paths: Optional[list[ImagePathSchema]] = Field(
        None,
        max_length=1,
        description="A list of image paths, with a maximum of one item.",
    )


class BiteArrayItemSchema(BaseModel):
    level: int
    class_: int = Field(alias="class")
    thickness: str
    area_total: float
    area_left: float
    area_right: float
    point_total: int
    point_left: int
    point_right: int
    rank: str


class BteDataSchema(BaseModel):
    business_number: str
    fi_data: FiDataSchema
    bite_array: list[BiteArrayItemSchema]
    comment: Optional[str] = None
    image_file_paths: Optional[list[ImagePathSchema]] = Field(
        None,
        max_length=1,
        description="A list of image paths, with a maximum of one item.",
    )
