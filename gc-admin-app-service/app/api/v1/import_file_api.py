from typing import Annotated, Optional

from configuration.settings import configuration
from core.common.api_response import ApiResponse
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase
from fastapi import APIRouter, Depends, File, UploadFile
from services.import_file_service import ImportFileService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.common.s3_bucket import S3Client
from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post("/medical-devices", summary="Api import file type medical device")
@version(1, 0)
async def medical_device_upload_file(
    db_session: Annotated[AsyncSession, Depends(CentralDatabase.get_db_session)],
    json_file: UploadFile = File(  # noqa: B008
        ..., description="The JSON data file from the medical device."
    ),
    image_files: Optional[list[UploadFile]] = File(  # noqa: B008
        None, description="An optional list of image files for the medical device."
    ),
):
    try:
        s3_client = await S3Client.get_instance(configuration=configuration)
        import_file_service = ImportFileService(db_session, s3_client)
        import_file_log_id = await import_file_service.medical_device_upload_file(
            json_file=json_file, image_files=image_files
        )
        return ApiResponse.success(
            data={"import_file_log_id": import_file_log_id},
            message=CustomMessageCode.IMPORT_FILE_UPLOAD_SUCCESS.title,
        )
    except CustomValueError as e:
        log.error(
            f"❌ Error upload_import_file CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error("❌ Import file error: {}".format(str(e)))
        return ApiResponse.error(
            message=CustomMessageCode.IMPORT_FILE_UPLOAD_FAILED.title,
            message_code=CustomMessageCode.IMPORT_FILE_UPLOAD_FAILED.code,
        )
