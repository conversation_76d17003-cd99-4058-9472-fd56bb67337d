from typing import AsyncGenerator, ClassVar, Optional

from configuration.context.tenant_context import get_current_db_name
from configuration.settings import Settings
from sqlalchemy.engine import URL
from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)

from gc_dentist_shared.core.logger.config import log


class CentralDatabase:
    _engine = None
    _sessionmaker: Optional[async_sessionmaker] = None

    @classmethod
    def get_url_db_sync(cls, db_name: str) -> str:
        return URL.create(
            drivername="postgresql",
            username=Settings().POSTGRES_USER,
            password=Settings().POSTGRES_PASSWORD,
            host=Settings().POSTGRES_SERVER,
            port=Settings().POSTGRES_PORT,
            database=db_name,
        )

    @classmethod
    def build_url_by_db_name(cls, db_name: str):
        return URL.create(
            drivername="postgresql+asyncpg",
            username=Settings().POSTGRES_USER,
            password=Settings().POSTGRES_PASSWORD,
            host=Settings().POSTGRES_SERVER,
            port=Settings().POSTGRES_PORT,
            database=db_name,
        )

    @classmethod
    def get_url(cls):
        return URL.create(
            drivername="postgresql+asyncpg",
            username=Settings().POSTGRES_USER,
            password=Settings().POSTGRES_PASSWORD,
            host=Settings().POSTGRES_SERVER,
            port=Settings().POSTGRES_PORT,
            database=Settings().POSTGRES_GLOBAL_DB_NAME,
        )

    @classmethod
    def get_async_engine(cls):
        if cls._engine is None:
            cls._engine = create_async_engine(
                cls.get_url(),
                echo=Settings().DB_ECHO,
                future=True,
                pool_pre_ping=True,
            )
        return cls._engine

    @classmethod
    def get_sessionmaker(cls) -> async_sessionmaker:
        if cls._sessionmaker is None:
            engine = cls.get_async_engine()
            cls._sessionmaker = async_sessionmaker(
                bind=engine, expire_on_commit=False, autoflush=False
            )
        return cls._sessionmaker

    @classmethod
    async def get_session_maker_factory(cls):
        session_maker = cls.get_sessionmaker()
        yield session_maker

    @classmethod
    async def get_db_session(cls) -> AsyncSession:
        session_maker = cls.get_sessionmaker()
        async_session: AsyncSession = session_maker()  # create session from factory
        try:
            yield async_session
        except Exception as e:
            log.error(f"❌ Error in database session: {e}")
            raise e
        finally:
            await async_session.close()

    @classmethod
    async def get_instance_db(cls) -> AsyncSession:
        session_maker = cls.get_sessionmaker()
        async_session: AsyncSession = session_maker()
        return async_session


class TenantDatabase:
    _engines: ClassVar[dict[str, AsyncEngine]] = {}
    _sessionmakers: ClassVar[dict[str, async_sessionmaker]] = {}

    # configure tenant db
    @classmethod
    def get_url_db_sync(cls, db_name: str) -> str:
        return URL.create(
            drivername="postgresql",
            username=Settings().POSTGRES_USER,
            password=Settings().POSTGRES_PASSWORD,
            host=Settings().POSTGRES_SERVER,
            port=Settings().POSTGRES_PORT,
            database=db_name,
        )

    @classmethod
    def get_url(cls, db_name: str) -> str:
        return URL.create(
            drivername="postgresql+asyncpg",
            username=Settings().POSTGRES_USER,
            password=Settings().POSTGRES_PASSWORD,
            host=Settings().POSTGRES_SERVER,
            port=Settings().POSTGRES_PORT,
            database=db_name,
        )

    @classmethod
    def get_engine_for_tenant(cls, db_name: str) -> AsyncEngine:
        if db_name not in cls._engines:
            db_url = cls.get_url(db_name)
            cls._engines[db_name] = create_async_engine(
                db_url,
                echo=Settings().DB_ECHO,
                future=True,
                pool_pre_ping=True,
            )
        return cls._engines[db_name]

    @classmethod
    def get_sessionmaker_for_tenant(cls) -> async_sessionmaker:
        db_name = get_current_db_name()
        if db_name not in cls._sessionmakers:
            engine = cls.get_engine_for_tenant(db_name)
            cls._sessionmakers[db_name] = async_sessionmaker(
                bind=engine, expire_on_commit=False, autoflush=False
            )
        return cls._sessionmakers[db_name]

    @classmethod
    async def get_tenant_session_maker_factory(cls) -> AsyncSession:
        session_maker = cls.get_sessionmaker_for_tenant()
        yield session_maker

    @classmethod
    async def get_tenant_db_session(cls) -> AsyncGenerator[AsyncSession, None]:
        session_maker = cls.get_sessionmaker_for_tenant()
        async_session: AsyncSession = session_maker()  # create session from factory
        try:
            yield async_session
        except Exception as e:
            log.error(f"❌ Error in database session: {e}")
            raise e
        finally:
            await async_session.close()

    @classmethod
    async def get_instance_tenant_db(cls) -> AsyncSession:
        session_maker = cls.get_sessionmaker_for_tenant()
        async_session: AsyncSession = session_maker()
        return async_session
