from enum import Enum

from gc_dentist_shared.core.common.i18n import i18n as _

ERROR_PROCESS = "Request could not be processed, please try again."
INIT_CLINIC_INPROCESS = "Init clinic in process."


class CustomMessageCode(Enum):
    """Custom message codes for application-specific errors and messages."""

    def __new__(cls, code, title, description):
        obj = object.__new__(cls)
        obj._value_ = code
        obj._title = title
        obj._description = description
        return obj

    @property
    def code(self):
        return self.value

    @property
    def title(self):
        # Using the i18n instance to translate the title
        return _(self._title)

    @property
    def description(self):
        return self._description

    def __str__(self):
        return f"{self.code} - {self.title}: {self.description}"

    # Server Errors
    UNKNOWN_ERROR = 5000, "Unknown error!", "An unexpected error occurred."
    TWILIO_ERROR = (
        5001,
        "Twilio error!",
        "An error occurred while processing the Twilio request.",
    )
    TWILIO_SEND_MESSAGE_ERROR = (
        5002,
        "Twilio send message error!",
        "An error occurred while sending a message via Twilio.",
    )
    S3_BUCKET_ERROR = (
        5003,
        "S3 bucket error!",
        "An error occurred while accessing the S3 bucket.",
    )

    # Tenant Clinic
    X_TENANT_SLUG_IS_REQUIRED = (
        4000,
        "X-Tenant-Slug header is required!",
        "The X-Tenant-Slug header must be provided in the request.",
    )
    TENANT_NOT_FOUND = (
        4004,
        "Tenant not found!",
        "The specified tenant does not exist.",
    )

    CLINIC_INFO_NOT_FOUND = (
        4005,
        "Clinic information not found!",
        "The specified clinic information does not exist.",
    )
    CLINIC_CREATED_SUCCESS = (
        4006,
        "Clinic created successfully!",
        "The clinic has been created successfully.",
    )
    CLINIC_CREATED_FAILED = (
        4007,
        "Clinic creation failed!",
        "An error occurred while creating the clinic.",
    )
    CLINIC_NOT_FOUND = (
        4008,
        "Clinic not found!",
        "The specified clinic does not exist.",
    )

    # Validation Errors
    VALUE_ERROR_INVALID_DATE_PAST = (
        10000,
        "Invalid date provided!",
        "The date provided must be in the past.",
    )

    # Source file
    IMPORT_FILE_UPLOAD_FAILED = (
        6000,
        "File upload failed!",
        "The file could not be processed.",
    )
    IMPORT_FILE_INVALID_FORMAT = (
        6001,
        "Invalid file format or content file error!",
        "The file content is not valid JSON or does not match the required data structure.",
    )
    IMPORT_FILE_UNSUPPORTED_SOURCE = (
        6002,
        "Unsupported source!",
        "The specified source is not supported by this endpoint.",
    )
    IMPORT_FILE_INVALID_DEVICE_TYPE = (
        6003,
        "Invalid device type!",
        "The 'type' field in 'fi_data' is missing or unsupported.",
    )
    IMPORT_FILE_MISSING_DATA_ID = (
        6004,
        "Missing data ID!",
        "The 'id' field in 'fi_data' is required.",
    )
    IMPORT_FILE_UPLOAD_SUCCESS = (
        6005,
        "File upload successfully",
        "File upload successfully",
    )
    IMPORT_FILE_INVALID_IMAGE_TYPE = (
        6006,
        "Invalid image file type!",
        "The uploaded image file has an unsupported format.",
    )
    IMPORT_FILE_MISSING_BUSINESS_NUMBER = (
        6007,
        "Missing business number!",
        "The required 'business_number' field is missing from the JSON payload.",
    )
