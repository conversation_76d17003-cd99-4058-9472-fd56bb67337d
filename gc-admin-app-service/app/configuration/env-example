ENVIRONMENT='develop'
PROJECT_NAME='GC Admin Service'
BACKEND_CORS_ORIGINS='["http://localhost:3000"]'

POSTGRES_SERVER=
POSTGRES_USER=
POSTGRES_PASSWORD=
POSTGRES_GLOBAL_DB_NAME=
POSTGRES_PORT=5432
DB_ECHO=false
DB_INIT=false

READ_ONLY_POSTGRES_SERVER=
READ_ONLY_POSTGRES_USER=
READ_ONLY_POSTGRES_PASSWORD=
READ_ONLY_POSTGRES_GLOBAL_DB_NAME=
READ_ONLY_POSTGRES_PORT=5432


# config db name using check migration is correct
POSTGERS_TENANT_TEMPLATE_DB_NAME = "tenant_template"

# Template Database for clinic tenant using build migration files 
DB_NAME_TEMPLATE = 'clinic_template'

#AWS
AES_SECRET_ID_ROTATION=
AWS_SECRET_ROTATION_KEY_MAPPING=
AWS_SECRET_CURRENT_VERSION=
AES_SECRET_KEY_MAPPING=

SES_REGION_NAME=
SES_FROM_MAIL=

# Communication Secret Key
COMMUNICATE_SECRET_KEY=

AES_SECRET_ID_ROTATION=
AWS_SECRET_ROTATION_KEY_MAPPING=
AWS_SECRET_CURRENT_VERSION=
AES_SECRET_KEY_MAPPING=

BASE_URL_DENTIST_SERVICE=
URL_INIT_ADMIN_DENTIST_SERVICE=

# Redis Local
REDIS_HOST=
REDIS_PORT=
REDIS_PASSWORD
REDIS_DATABASE=
REDIS_TIMEOUT=

# Twilio configuration
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_SERVICE_SID=
TWILIO_MESSAGE_SERVICE_SID=

#Firebase
FIREBASE_ENABLED=
FIREBASE_DRY_RUN=
FIREBASE_CERT_PATH=

# AWS S3 configuration
S3_BUCKET_NAME=
S3_FOLDER_NAME=
EXPIRED_GENERATE_PRESIGNED_URL=
