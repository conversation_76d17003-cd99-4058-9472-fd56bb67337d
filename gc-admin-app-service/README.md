
### 1.8 Internationalization

Get pybabel tools directly within your FastAPI project without hassle.


- Add your own language locale directory, for instance ja_<PERSON>.
    ```sh
    PYTHONPATH=$PYTHONPATH:$(pwd) python core/common/babel_cli.py init -l ja_JP
    PYTHONPATH=$PYTHONPATH:$(pwd) python core/common/babel_cli.py init -l en_US
    ```

- Extract messages with following command
    ```sh
    cd gc-admin-app-service/app
    PYTHONPATH=$PYTHONPATH:$(pwd) python core/common/babel_cli.py extract -d .
    ```
    => When you have "venv" forder
    ```sh
    PYTHONPATH=$PYTHONPATH:$(pwd) python core/common/babel_cli.py extract -d . --ignore-dirs venv
    ```

- If you have already extracted messages and you have an existing `.po` and `.mo` file.
    ```sh
    PYTHONPATH=$PYTHONPATH:$(pwd) python core/common/babel_cli.py update
    ```
    
- Run mapping default language
    ```sh
    python core/common/extract_default_translation.py
    ```

- Compile Message
    ```sh
    PYTHONPATH=$PYTHONPATH:$(pwd) python core/common/babel_cli.py compile -l ja_JP
    PYTHONPATH=$PYTHONPATH:$(pwd) python core/common/babel_cli.py compile -l en_US
    ```

