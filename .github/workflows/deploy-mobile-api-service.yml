name: Deploy GC Mobile API Service

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target Environment'
        required: true
        type: choice
        options:
          - develop
          - staging
          - production

permissions:
  id-token: write
  contents: read

jobs:
  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    steps:
      - name: Notify build start
        uses: hoangbvh/actions-google-chat-notification@v1.0.0
        with:
          name: "[${{ github.event.inputs.environment }}] Build GC-Dentist-Service:"
          url: ${{ secrets.GOOGLE_CHAT_WEBHOOK }}
          status: in-progress

      - name: Checkout
        uses: actions/checkout@v4

      - name: Get Short Commit Hash and Date
        id: vars
        run: |
          echo "short_sha=${GITHUB_SHA::7}" >> $GITHUB_OUTPUT
          echo "date=$(date +'%Y%m%d.%H%M')" >> $GITHUB_OUTPUT

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          role-to-assume: ${{ secrets.AWS_GITHUB_ACTIONS_ROLE }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build and push Docker images
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          file: ./gc-mobile-api-service/Dockerfile
          tags: |
            ${{ vars.GC_MOBILE_API_SERVICE_ECR }}:${{ steps.vars.outputs.short_sha }}.${{ steps.vars.outputs.date }}
            ${{ vars.GC_MOBILE_API_SERVICE_ECR }}:latest

      - name: Deploy GC Mobile API Service
        if: ${{ github.event.inputs.environment == 'develop' }}
        uses: newarifrh/portainer-service-webhook@v1
        with:
          webhook_url: ${{ secrets.GC_MOBILE_API_SERVICE_WEBHOOK }}?tag=${{ steps.vars.outputs.short_sha }}.${{ steps.vars.outputs.date }}

      - name: Notify build failure
        if: failure()
        uses: hoangbvh/actions-google-chat-notification@v1.0.0
        with:
          name: "[${{ github.event.inputs.environment }}] Build GC-Dentist-Service:"
          url: ${{ secrets.GOOGLE_CHAT_WEBHOOK }}
          status: failure

      - name: Notify build success
        if: success()
        uses: hoangbvh/actions-google-chat-notification@v1.0.0
        with:
          name: "[${{ github.event.inputs.environment }}] Build GC-Dentist-Service:"
          url: ${{ secrets.GOOGLE_CHAT_WEBHOOK }}
          status: success
