from typing import Optional

# from configuration.context.tenant_context import get_current_db_name
# from configuration.logger.config import log
from configuration.settings import Settings
from sqlalchemy import create_engine
from sqlalchemy.engine import URL
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import sessionmaker


class CentralDatabase:
    _engine = None
    _sessionmaker: Optional[async_sessionmaker] = None

    @classmethod
    def get_url_db_sync(cls, db_name: str) -> str:
        return URL.create(
            drivername="postgresql",
            username=Settings().POSTGRES_USER,
            password=Settings().POSTGRES_PASSWORD,
            host=Settings().POSTGRES_SERVER,
            port=Settings().POSTGRES_PORT,
            database=db_name,
        )

    @classmethod
    def get_url(cls):
        return URL.create(
            drivername="postgresql+asyncpg",
            username=Settings().POSTGRES_USER,
            password=Settings().POSTGRES_PASSWORD,
            host=Settings().POSTGRES_SERVER,
            port=Settings().POSTGRES_PORT,
            database=Settings().POSTGRES_GLOBAL_DB_NAME,
        )

    @classmethod
    def get_async_engine(cls):
        if cls._engine is None:
            cls._engine = create_async_engine(
                cls.get_url(),
                echo=Settings().DB_ECHO,
                future=True,
                pool_pre_ping=True,
            )
        return cls._engine

    @classmethod
    def get_sessionmaker(cls) -> async_sessionmaker:
        if cls._sessionmaker is None:
            engine = cls.get_async_engine()
            cls._sessionmaker = async_sessionmaker(
                bind=engine, expire_on_commit=False, autoflush=False
            )
        return cls._sessionmaker

    @classmethod
    async def get_session_maker_factory(cls) -> AsyncSession:
        async_session = cls.get_sessionmaker()
        yield async_session

    @classmethod
    async def get_db_session(cls) -> AsyncSession:
        session_maker = cls.get_sessionmaker()
        async_session: AsyncSession = session_maker()  # create session from factory
        try:
            yield async_session
        except Exception as e:
            # log.error(f"❌ Error in database session: {e}")
            raise e
        finally:
            await async_session.close()

    @classmethod
    async def get_instance_db(cls) -> AsyncSession:
        session_maker = cls.get_sessionmaker()
        async_session: AsyncSession = session_maker()
        return async_session

    # Config for database sync using flask
    @classmethod
    def get_url_db_central_sync(cls) -> str:
        return URL.create(
            drivername="postgresql+psycopg2",
            username=Settings().POSTGRES_USER,
            password=Settings().POSTGRES_PASSWORD,
            host=Settings().POSTGRES_SERVER,
            port=Settings().POSTGRES_PORT,
            database=Settings().POSTGRES_GLOBAL_DB_NAME,
        )

    @classmethod
    def get_sync_session_maker_factory(cls):
        # --- Config for Flask (Sync) ---
        sync_engine = create_engine(
            cls.get_url_db_central_sync(),
            echo=Settings().DB_ECHO,
            future=True,
            pool_pre_ping=True,
        )
        return sessionmaker(autocommit=False, autoflush=False, bind=sync_engine)

    @classmethod
    def get_sync_db_session(cls):
        factory = cls.get_sync_session_maker_factory()
        db = factory()
        try:
            yield db
        finally:
            db.close()

    @classmethod
    def get_sync_db_session_instance(cls):
        factory = cls.get_sync_session_maker_factory()
        db = factory()
        return db


# class TenantDatabase:
#     _engines: ClassVar[dict[str, AsyncEngine]] = {}
#     _sessionmakers: ClassVar[dict[str, async_sessionmaker]] = {}

#     _sync_engines: ClassVar[dict[str, Engine]] = {}
#     _sync_sessionmakers: ClassVar[dict[str, sessionmaker]] = {}

#     # configure tenant db
#     @classmethod
#     def get_url_db_sync(cls, db_name: str) -> str:
#         return URL.create(
#             drivername="postgresql",
#             username=Settings().POSTGRES_USER,
#             password=Settings().POSTGRES_PASSWORD,
#             host=Settings().POSTGRES_SERVER,
#             port=Settings().POSTGRES_PORT,
#             database=db_name,
#         )

#     @classmethod
#     def get_url(cls, db_name: str) -> str:
#         return URL.create(
#             drivername="postgresql+asyncpg",
#             username=Settings().POSTGRES_USER,
#             password=Settings().POSTGRES_PASSWORD,
#             host=Settings().POSTGRES_SERVER,
#             port=Settings().POSTGRES_PORT,
#             database=db_name,
#         )

#     @classmethod
#     def get_engine_for_tenant(cls, db_name: str) -> AsyncEngine:
#         if db_name not in cls._engines:
#             db_url = cls.get_url(db_name)
#             cls._engines[db_name] = create_async_engine(
#                 db_url,
#                 echo=Settings().DB_ECHO,
#                 future=True,
#                 pool_pre_ping=True,
#             )
#         return cls._engines[db_name]

#     @classmethod
#     def get_sessionmaker_for_tenant(cls) -> async_sessionmaker:
#         db_name = get_current_db_name()
#         if db_name not in cls._sessionmakers:
#             engine = cls.get_engine_for_tenant(db_name)
#             cls._sessionmakers[db_name] = async_sessionmaker(
#                 bind=engine, expire_on_commit=False, autoflush=False
#             )
#         return cls._sessionmakers[db_name]

#     @classmethod
#     async def get_tenant_session_maker_factory(cls) -> AsyncSession:
#         session_maker = cls.get_sessionmaker_for_tenant()
#         yield session_maker

#     @classmethod
#     async def get_tenant_db_session(cls) -> AsyncGenerator[AsyncSession, None]:
#         session_maker = cls.get_sessionmaker_for_tenant()
#         async_session: AsyncSession = session_maker()  # create session from factory
#         try:
#             yield async_session
#         except Exception as e:
#             log.error(f"❌ Error in database session: {e}")
#             raise e
#         finally:
#             await async_session.close()

#     @classmethod
#     async def get_instance_tenant_db(cls) -> AsyncSession:
#         session_maker = cls.get_sessionmaker_for_tenant()
#         async_session: AsyncSession = session_maker()
#         return async_session

#     # Config for database sync using flask
#     @classmethod
#     def get_sync_engine_for_tenant(cls, db_name: str) -> AsyncEngine:
#         if db_name not in cls._sync_engines:
#             db_url = cls.get_url_db_sync(db_name)
#             cls._sync_engines[db_name] = create_engine(
#                 db_url,
#                 echo=Settings().DB_ECHO,
#                 future=True,
#                 pool_pre_ping=True,
#             )
#         return cls._sync_engines[db_name]

#     @classmethod
#     def get_sync_sessionmaker_for_tenant(cls) -> sessionmaker:
#         db_name = get_current_db_name()
#         if db_name not in cls._sync_sessionmakers:
#             engine = cls.get_engine_for_tenant(db_name)
#             cls._sync_sessionmakers[db_name] = sessionmaker(
#                 bind=engine, expire_on_commit=False, autoflush=False
#             )
#         return cls._sync_sessionmakers[db_name]

#     @classmethod
#     def get_sync_db_session(cls):
#         db_session = cls.get_sync_sessionmaker_for_tenant()
#         try:
#             yield db_session
#         finally:
#             db_session.close()
