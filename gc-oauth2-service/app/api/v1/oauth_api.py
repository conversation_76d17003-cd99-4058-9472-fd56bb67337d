from auth_server.oauth2 import authorization
from flask import Blueprint, redirect, render_template, request, session, url_for
from services.auth_service import create_oauth_client
from services.user_service import get_global_user_by_id, get_global_user_by_name

bp = Blueprint("oauth", __name__)


@bp.route("/login", methods=["GET", "POST"])
def login():
    if request.method == "POST":
        username = request.form.get("username")
        user = get_global_user_by_name(username)
        if user:
            session["user_id"] = user.id
            next_url = request.args.get("next", "/")
            return redirect(next_url)
    return render_template("login.html")


@bp.route("/oauth/authorize", methods=["GET", "POST"])
def authorize():
    user_id = session.get("user_id")
    user = get_global_user_by_id(user_id)
    if not user:
        return redirect(url_for("oauth.login", next=request.url))

    if request.method == "GET":
        grant = authorization.get_consent_grant(request, end_user=user)
        return render_template("authorize.html", grant=grant, user=user)

    confirm = request.form.get("confirm")
    grant_user = user if confirm == "yes" else None
    return authorization.create_authorization_response(request, grant_user=grant_user)


@bp.route("/oauth/token", methods=["POST"])
def issue_token():
    return authorization.create_token_response()


@bp.route("/oauth/revoke", methods=["POST"])
def revoke_token():
    return authorization.create_endpoint_response("revocation")


@bp.route("/create_client", methods=("GET", "POST"))
def create_client():
    # username = request.form.get('username')
    # db: SQLAlchemySession = next(CentralDatabase.get_sync_db_session())
    # user = db.execute(select(GlobalUser)
    #        .where(GlobalUser.username == "<EMAIL>"))
    #        .scalar_one_or_none()

    # if not user:
    #     return redirect('/')

    if request.method == "GET":
        return render_template("create_client.html")

    form = request.form
    create_oauth_client(form)

    return redirect("/")
