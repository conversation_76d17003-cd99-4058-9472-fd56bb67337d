from configuration.logger.config import log
from core.common.api_response import ApiResponse
from core.decorators.inject_database_session import inject_db_session
from flask import Blueprint, jsonify, request
from schemas.auth_schema import LoginOTPRequest
from services.user_patient_service import UserPatientService

# from flask_api import status
from sqlalchemy.ext.asyncio import AsyncSession

bp = Blueprint("users", __name__)


@bp.route("/login/patient", methods=["POST"])
@inject_db_session
async def login(
    db_session: AsyncSession,
):
    try:

        json_data = request.get_json()
        if not json_data:
            return jsonify({"error": "Missing JSON in request"}), 400

        login_data = LoginOTPRequest(**json_data)
        auth_service = UserPatientService()
        response = await auth_service.login_for_patient(login_data.model_dump())
        return ApiResponse.success(data=response.model_dump_json())
    except Exception as e:
        import traceback

        traceback.print_exc()
        log.error(f"❌ Login failed with error: {str(e)}")
        return ApiResponse.error(
            message=str(e),
            status_code=401,
        )


# @router.post("/auth/login", response_model=OAuthTokenResponse)
# async def login(request: Request, data_login: LoginRequest):
#     tenant_slug = request.headers.get(X_TENANT_SLUG)
#     tenant_slug = tenant_slug.strip() if tenant_slug else None
#     if not tenant_slug:
#         return ApiResponse.error(
#             message=X_TENANT_SLUG_IS_REQUIRED,
#             message_code=status.HTTP_400_BAD_REQUEST,
#         )
#     try:
#         auth_service = AuthServices()
#         response = await auth_service.login_for_doctor(tenant_slug, data_login)
#         return ApiResponse.success(
#             data=response.model_dump()
#         )
#     except Exception as e:
#         log.error(f"❌ Login failed for tenant '{tenant_slug}': {str(e)}")
#         return ApiResponse.error(
#             message=str(e),
#             status_code=status.HTTP_401_UNAUTHORIZED,
#         )
