import random
import string

from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from configuration.settings import configuration
from core.constants import DOCTOR_LOGIN_REDIS_PREFIX, OTP_EXPIRY_MINUTES, OTP_LENGTH
from core.messages import CustomMessageCode
from db.db_connection import CentralDatabase, TenantDatabase
from fastapi_mail import MessageType
from schemas.auth_schema import (
    DoctorLoginOTPRequest,
    DoctorLoginRequest,
    DoctorLoginResponse,
    OAuthTokenResponse,
)
from services.auth_service import OAuth2ClientService
from sqlalchemy import select

from gc_dentist_shared.central_models import MailT<PERSON>plate, OAuth2Client, TenantClinic
from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.common.jwt_token import encode_jwt_token
from gc_dentist_shared.core.common.mail import MailRequest
from gc_dentist_shared.core.common.synchronous_mail import Sync<PERSON><PERSON><PERSON>lient
from gc_dentist_shared.core.common.synchronous_redis import SyncRedis<PERSON><PERSON>
from gc_dentist_shared.core.constants import UTF8
from gc_dentist_shared.core.enums.mail_enum import MailTemplateCategoryEnum
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import DoctorUser


class DoctorAuthService:

    def authenticate_user(
        self, tenant_slug: str, obj: DoctorLoginRequest
    ) -> DoctorLoginResponse:
        token = None
        try:
            tenant = self.get_tenant_by_tenant_slug(tenant_slug)
            tenant_uuid = str(tenant.tenant_uuid)
            self.get_internal_oauth_client(obj.client_id)

            token = set_current_db_name(tenant.db_name)
            doctor_user = self.verify_doctor_user_credentials(
                username=obj.username,
                plain_password=obj.password,
                tenant_uuid=tenant_uuid,
            )

            otp = self.create_and_save_otp(
                tenant_uuid=tenant_uuid,
                user_id=doctor_user.id,
            )

            mail_template = self.get_mail_template(
                template_category=MailTemplateCategoryEnum.LOGIN_FOR_DOCTOR.value
            )
            self.send_otp_to_mail(
                doctor_email=doctor_user.username,
                template=mail_template,
                data={"otp": otp, "expire_minutes": OTP_EXPIRY_MINUTES},
            )

            return DoctorLoginResponse(user_id=doctor_user.id)

        except CustomValueError as e:
            log.error(
                f"❌ Failed to authenticate doctor: {obj.username}, error: {str(e)}"
            )
            raise e

        except Exception as e:
            log.error(
                f"❌ Failed to authenticate doctor: {obj.username}, error: {str(e)}"
            )
            raise e

        finally:
            if token:
                reset_current_db_name(token)

    def verify_otp(
        self, tenant_slug: str, obj: DoctorLoginOTPRequest
    ) -> OAuthTokenResponse:
        """
        Verify the email OTP and generate access/refresh tokens

        :param tenant_slug: The tenant slug
        :param obj: The verification data containing username and OTP
        :return: Response with access and refresh tokens
        """
        token = None
        try:
            tenant = self.get_tenant_by_tenant_slug(tenant_slug)
            tenant_uuid = str(tenant.tenant_uuid)
            client = self.get_internal_oauth_client(obj.client_id)
            token = set_current_db_name(tenant.db_name)

            doctor_user = self.get_doctor_user(obj.user_id)

            redis = SyncRedisCli.get_instance(configuration)
            aes_gcm = AesGCMRotation(configuration)
            opt_key = f"{DOCTOR_LOGIN_REDIS_PREFIX}_{tenant_uuid}_{aes_gcm.sha256_hash(obj.user_id)}"
            otp_data = redis.get(opt_key)

            # Case OTP expired or not found -> resend OTP
            if not otp_data:
                raise CustomValueError(
                    message=CustomMessageCode.OTP_NOT_FOUND_OR_EXPIRED.title,
                    message_code=CustomMessageCode.OTP_NOT_FOUND_OR_EXPIRED.code,
                )

            # Case OTP incorrect
            if otp_data != obj.otp:
                raise CustomValueError(
                    message=CustomMessageCode.OTP_INCORRECT.title,
                    message_code=CustomMessageCode.OTP_INCORRECT.code,
                )

            redis.delete(opt_key)

            return self.create_access_token(
                internal_client=client,
                user=doctor_user,
                tenant_uuid=tenant_uuid,
            )

        except CustomValueError as e:
            log.error(
                f"❌ Failed to verify OTP for doctor: {obj.user_id}, error: {str(e)}"
            )
            raise e

        except Exception as e:
            log.error(
                f"❌ Failed to verify OTP for doctor: {obj.user_id}, error: {str(e)}"
            )
            raise e

        finally:
            if token:
                reset_current_db_name(token)

    def get_tenant_by_tenant_slug(self, tenant_slug: str) -> TenantClinic:
        central_db_session = next(CentralDatabase.get_sync_db_session())
        try:
            result = central_db_session.execute(
                select(TenantClinic).where(TenantClinic.tenant_slug == tenant_slug)
            )
            tenant = result.scalar_one_or_none()

            if not tenant:
                raise CustomValueError(
                    message=CustomMessageCode.X_TENANT_NOT_FOUND.title,
                    message_code=CustomMessageCode.X_TENANT_NOT_FOUND.code,
                )

        except CustomValueError as e:
            log.error(f"❌ Tenant not found with tenant slug: {tenant_slug}")
            raise e

        except Exception as e:
            log.error(f"❌ Database error while finding tenant: {str(e)}")
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            )

        finally:
            central_db_session.close()

        return tenant

    def get_internal_oauth_client(self, client_id: str):
        central_db_session = next(CentralDatabase.get_sync_db_session())
        try:
            result = central_db_session.execute(
                select(OAuth2Client).where(OAuth2Client.client_id == client_id)
            )
            client = result.scalar_one_or_none()

            if not client:
                raise CustomValueError(
                    message=CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.title,
                    message_code=CustomMessageCode.INTERNAL_CLIENT_NOT_FOUND.code,
                )

        except CustomValueError as e:
            log.error(f"❌ Oauth client not found with client_id: {client_id}")
            raise e

        except Exception as e:
            log.error(f"❌ Database error while finding oauth client: {str(e)}")
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            )

        finally:
            central_db_session.close()

        return client

    def get_doctor_user(self, user_id: int) -> DoctorUser:
        tenant_db_session = next(TenantDatabase.get_sync_db_session())
        try:
            stmt = select(DoctorUser).where(
                DoctorUser.id == user_id, DoctorUser.status.is_(True)
            )
            doctor_user = tenant_db_session.execute(stmt).scalar_one_or_none()
            if not doctor_user:
                raise CustomValueError(
                    message=CustomMessageCode.DOCTOR_USER_NOT_FOUND.title,
                    message_code=CustomMessageCode.DOCTOR_USER_NOT_FOUND.code,
                )

        except CustomValueError as e:
            log.error(f"❌ Doctor user not found for user_id: {user_id}")
            raise e

        except Exception as e:
            log.error(
                f"❌ Database error while verifying getting doctor user: {str(e)}"
            )
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            )

        finally:
            tenant_db_session.close()

        return doctor_user

    def get_mail_template(
        self, template_category: str, template_language: str = "ja"
    ) -> MailTemplate:
        """Get mail template from central database"""
        central_db_session = next(CentralDatabase.get_sync_db_session())
        try:
            result = central_db_session.execute(
                select(MailTemplate).where(
                    MailTemplate.category == template_category,
                    MailTemplate.language == template_language,
                    MailTemplate.status.is_(True),
                )
            )
            template = result.scalar_one_or_none()
            if not template:
                raise CustomValueError(
                    message=CustomMessageCode.MAIL_TEMPLATE_NOT_FOUND.title,
                    message_code=CustomMessageCode.MAIL_TEMPLATE_NOT_FOUND.code,
                )

        except CustomValueError as e:
            log.error(f"❌ Mail template not found for category: {template_category}")
            raise e

        except Exception as e:
            log.error(f"❌ Database error while finding mail template: {str(e)}")
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            )

        finally:
            central_db_session.close()

        return template

    def verify_doctor_user_credentials(
        self, username: str, plain_password: str, tenant_uuid: str
    ):
        tenant_db_session = next(TenantDatabase.get_sync_db_session())
        try:
            stmt = select(DoctorUser).where(
                DoctorUser.username == username, DoctorUser.status.is_(True)
            )
            doctor_user = tenant_db_session.execute(stmt).scalar_one_or_none()
            if not doctor_user or not doctor_user.validate_password(
                plain_password=plain_password,
                tenant_uuid=tenant_uuid,
            ):
                raise CustomValueError(
                    message=CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.title,
                    message_code=CustomMessageCode.INVALID_USERNAME_OR_PASSWORD.code,
                )

        except CustomValueError as e:
            log.error(f"❌ Invalid doctor user credentials for username: {username}")
            raise e

        except Exception as e:
            log.error(
                f"❌ Database error while verifying doctor user cretentials: {str(e)}"
            )
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            )

        finally:
            tenant_db_session.close()

        return doctor_user

    def create_and_save_otp(self, tenant_uuid: str, user_id: int) -> str:
        """
        Create an OTP and save it to Redis with required information

        :param tenant_uuid: Tenant UUID
        :param user_id: Doctor user ID
        :return: Generated OTP
        """
        redis = SyncRedisCli.get_instance(configuration)
        aes_gcm = AesGCMRotation(configuration)

        hased_user_id = aes_gcm.sha256_hash(user_id)
        otp = "".join(random.choices(string.digits, k=OTP_LENGTH))

        otp_key = f"{DOCTOR_LOGIN_REDIS_PREFIX}_{tenant_uuid}_{hased_user_id}"
        redis.setex(name=otp_key, time=OTP_EXPIRY_MINUTES * 60, value=otp)  # in seconds

        return otp

    def send_otp_to_mail(self, doctor_email: str, template: MailTemplate, data: dict):
        try:
            mail_client = SyncMailClient.get_instance(configuration)
            body_content = mail_client.render_body_content_template(
                template=template.content, data_bidding=data
            )

            # Create mail request
            mail_request = MailRequest(
                recipients=[doctor_email],
                subject=template.title,
                body=body_content,
                charset=UTF8,
                data_bidding=data,
                category=template.category,
                subtype=MessageType.plain,
            )

            result = mail_client.send_message(mail_request)
            if result:
                log.info(f"✅ OTP email sent successfully to doctor: {doctor_email}")
            else:
                raise CustomValueError(
                    message=CustomMessageCode.SEND_MAIL_OTP_FAILED.title,
                    message_code=CustomMessageCode.SEND_MAIL_OTP_FAILED.code,
                )

        except Exception as e:
            log.error(
                f"❌ Failed to send email to doctor: {doctor_email}, error: {str(e)}"
            )
            raise CustomValueError(
                message=CustomMessageCode.SEND_MAIL_OTP_FAILED.title,
                message_code=CustomMessageCode.SEND_MAIL_OTP_FAILED.code,
            )

    def create_access_token(
        self, internal_client: OAuth2Client, user: DoctorUser, tenant_uuid: str
    ) -> OAuthTokenResponse:
        try:
            token = encode_jwt_token(
                configuration.RSA_KEY_MANIFEST.get("current_kid"),
                configuration.JWT_RSA_PRIVATE_KEY,
                configuration.JWT_ALGORITHM,
                configuration.ACCESS_TOKEN_EXPIRE_MINUTES,
                "",
                internal_client,
                user,
                " ".join(configuration.DEFAULT_INTERNAL_SCOPES),
            )
            OAuth2ClientService.dynamic_save_token_internal_client_sync(
                token=token,
                client_id=internal_client.client_id,
                clinic_doctor_id=user.id,
                tenant_uuid=tenant_uuid,
            )
            return OAuthTokenResponse(
                access_token=token.get("access_token"),
                refresh_token=token.get("refresh_token"),
                token_type=token.get("token_type"),
                expires_in=token.get("expires_in"),
                tenant_uuid=tenant_uuid,
            )

        except CustomValueError as e:
            log.error(
                f"❌ Failed to create token for doctor: {user.username}, error: {str(e)}"
            )
            raise e

        except Exception as e:
            log.error(f"❌ Database error while creating token: {str(e)}")
            raise CustomValueError(
                message=CustomMessageCode.UNKNOWN_ERROR.title,
                message_code=CustomMessageCode.UNKNOWN_ERROR.code,
            )
