from typing import Optional

from configuration.settings import configuration
from core.messages import INVALID_USER
from db.db_connection import CentralDatabase
from schemas.auth_schema import OAuthTokenInternalResponse
from services.auth_service import get_internal_client, save_token_internal_client
from sqlalchemy import select

from gc_dentist_shared.central_models import GlobalUser, TenantPatientUserMapping
from gc_dentist_shared.core.common.jwt_token import encode_jwt_token


class UserPatientService:
    async def login_for_patient(self, patient_obj: dict):
        try:
            print("Login for patient ", patient_obj)
            return await self.authenticate_patient_user(patient_obj, None)
        except ValueError as e:
            raise ValueError(str(e))
        except Exception as e:
            import traceback

            traceback.print_exc()
            raise Exception(f"Authentication failed: {str(e)}")

    async def authenticate_patient_user(
        self, patient_obj: dict, tenant_uuid: Optional[str] = None
    ):
        session = await CentralDatabase.get_instance_db()
        async with session.begin():
            result = await session.execute(
                select(GlobalUser).where(
                    GlobalUser.username == patient_obj.get("phone_number"),
                    GlobalUser.is_active.is_(True),
                )
            )
            user = result.scalar_one_or_none()
            # TODO: Twilio verify OTP

            # if not user or not user.validate_password(obj.password):
            #     raise ValueError(INVALID_USERNAME_OR_PASSWORD)

            result_tenant_uuids = await session.execute(
                select(TenantPatientUserMapping.tenant_uuid).where(
                    TenantPatientUserMapping.global_user_id == user.id,
                )
            )
            tenant_uuids = result_tenant_uuids.scalars()
            if not tenant_uuids:
                raise ValueError(INVALID_USER)

            internal_client = await get_internal_client(
                session, client_name=configuration.DEFAULT_CLIENT_NAME_FOR_WEB
            )

            data_token = encode_jwt_token(
                configuration.RSA_KEY_MANIFEST.get("current_kid"),
                configuration.JWT_RSA_PRIVATE_KEY,
                configuration.JWT_ALGORITHM,
                configuration.ACCESS_TOKEN_EXPIRE_MINUTES,
                "",
                internal_client,
                user,
                " ".join(configuration.DEFAULT_INTERNAL_SCOPES),
            )

            await save_token_internal_client(
                session, data_token, internal_client.client_id, user.id
            )

            return OAuthTokenInternalResponse(
                access_token=data_token.get("access_token"),
                refresh_token=data_token.get("refresh_token"),
                token_type=data_token.get("token_type"),
                expires_in=data_token.get("expires_in"),
                tenant_uuids=list(tenant_uuids),
            )
