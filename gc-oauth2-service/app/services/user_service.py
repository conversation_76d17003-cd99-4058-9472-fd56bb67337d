from typing import Optional

from db.db_connection import CentralDatabase
from sqlalchemy import select
from sqlalchemy.orm import Session as SQLAlchemySession

from gc_dentist_shared.central_models import GlobalUser


def get_global_user_by_name(username: str):
    db_session: SQLAlchemySession = next(CentralDatabase.get_sync_db_session())
    try:
        user = db_session.execute(
            select(GlobalUser).where(GlobalUser.username == username)
        ).scalar_one_or_none()
        return user
    except Exception as e:
        print(f"Error get_global_user_by_name error: {str(e)}")
        return None
    finally:
        db_session.close()


def get_global_user_by_id(user_id: Optional[int]):
    if not user_id:
        return None
    db_session: SQLAlchemySession = next(CentralDatabase.get_sync_db_session())
    try:
        user = db_session.execute(
            select(GlobalUser).where(GlobalUser.id == user_id)
        ).scalar_one_or_none()
        return user
    except Exception as e:
        print(f"Error get_global_user_by_id error: {str(e)}")
        return None
    finally:
        db_session.close()
