# from typing import Optional
import time
from datetime import datetime, timezone

from configuration.settings import configuration
from db.db_connection import CentralDatabase
from sqlalchemy import delete, select
from sqlalchemy.orm import Session as SQLAlchemySession
from werkzeug.security import gen_salt

from gc_dentist_shared.central_models import OAuth2<PERSON><PERSON>, OAuth2Token


def split_by_crlf(s):
    return [v for v in s.splitlines() if v]


def create_oauth_client(form):
    db_session: SQLAlchemySession = next(CentralDatabase.get_sync_db_session())
    try:
        client_id = gen_salt(24)
        client_id_issued_at = int(time.time())
        client = OAuth2Client(
            name=form["client_name"],
            client_id=client_id,
            client_id_issued_at=client_id_issued_at,
        )
        client_metadata = {
            "client_name": form["client_name"],
            "client_uri": form["client_uri"],
            "grant_types": split_by_crlf(form["grant_type"]),
            "redirect_uris": split_by_crlf(form["redirect_uri"]),
            "response_types": split_by_crlf(form["response_type"]),
            "scope": form["scope"],
            "token_endpoint_auth_method": form["token_endpoint_auth_method"],
        }
        client.set_client_metadata(client_metadata)

        if form["token_endpoint_auth_method"] == "none":
            client.client_secret = ""
        else:
            client.client_secret = gen_salt(48)

        db_session.add(client)
        db_session.commit()
    except Exception as e:
        print(f"Error creating OAuth client: {str(e)}")
        raise e
    finally:
        db_session.close()


async def save_token_internal_client(
    db_session, token: dict, client_id: str, user_id: int
):
    # db_session: SQLAlchemySession = next(CentralDatabase.get_sync_db_session())
    try:
        # client = request.client
        # user = request.user

        # if request.payload.grant_type == 'refresh_token':

        delete_stmt = delete(OAuth2Token).where(OAuth2Token.global_user_id == user_id)
        await db_session.execute(delete_stmt)
        new_token = OAuth2Token(
            client_id=client_id,
            global_user_id=user_id,
            token_type=token.get("token_type"),
            access_token=token.get("access_token"),
            refresh_token=token.get("refresh_token"),
            scope=token.get("scope"),
            issued_at=int(datetime.now(timezone.utc).timestamp()),
            refresh_token_expires_at=int(time.time())
            + (configuration.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60),
            expires_in=token.get("expires_in"),
        )
        db_session.add(new_token)
        await db_session.flush()
    except Exception as e:
        print(f"Error saving token: {str(e)}")
        raise e


async def get_internal_client(
    db_session, client_name: str = configuration.DEFAULT_CLIENT_NAME_FOR_WEB
):
    result = await db_session.execute(
        select(OAuth2Client).where(OAuth2Client.name == client_name)
    )
    client = result.scalar_one_or_none()
    return client
